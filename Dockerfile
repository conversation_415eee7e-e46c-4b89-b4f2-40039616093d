# EchoNote FastAPI应用Dockerfile
# 基于Python 3.11官方镜像，遵循Context7最佳实践

# 使用官方Python 3.11 slim镜像作为基础镜像
FROM python:3.11-slim as base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    POETRY_VERSION=1.6.1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry==$POETRY_VERSION

# 配置Poetry
ENV POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# 设置工作目录
WORKDIR /app

# 复制Poetry配置文件
COPY pyproject.toml poetry.lock* ./

# 安装依赖
RUN poetry install --only=main --no-root && rm -rf $POETRY_CACHE_DIR

# 开发阶段
FROM base as development

# 安装开发依赖
RUN poetry install --no-root && rm -rf $POETRY_CACHE_DIR

# 复制应用代码
COPY . .

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /fast_app
USER appuser

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["poetry", "run", "python", "apps/run.py", "--env", "development"]

# 生产阶段
FROM base as production

# 复制应用代码
COPY . .

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /fast_app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# 启动命令
CMD ["poetry", "run", "python", "apps/run.py", "--env", "production"]
