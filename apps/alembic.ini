# EchoNote项目的Alembic数据库迁移配置
#
# 该配置文件用于管理SQLAlchemy数据库迁移，支持自动生成迁移脚本
# 和版本控制，确保数据库结构的一致性和可追溯性。

[alembic]
# 迁移脚本存放路径
script_location = migrations

# 迁移文件命名模板，包含时间戳便于排序和识别
# 格式：年月日_时分秒-版本号_描述
file_template = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d%%(second).2d-%%(rev)s_%%(slug)s

# Python路径配置，确保能正确导入项目模块
# 将当前目录添加到sys.path，使Alembic能找到项目模块
prepend_sys_path = .

# 时区设置，用于迁移文件中的时间戳
# 使用UTC时区确保时间的一致性
timezone = UTC

# 描述字段的最大长度限制
# 防止文件名过长导致文件系统问题
truncate_slug_length = 40

# 在revision命令期间运行环境脚本
# 启用此选项以支持自定义钩子和验证
revision_environment = true

# 允许检测没有源.py文件的.pyc和.pyo文件
# 在生产环境中可能有用，但通常保持关闭
sourceless = false

# 版本号格式，使用时间戳格式便于排序
version_num_format = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d%%(second).2d

# 版本路径分隔符，使用操作系统默认分隔符
version_path_separator = os

# 递归搜索版本目录
# 支持在子目录中组织迁移文件
recursive_version_locations = false

# 输出编码，确保支持中文注释
output_encoding = utf-8

# 数据库连接URL
# 注意：实际使用时应从环境变量或配置文件中读取
# 格式：postgresql://username:password@host:port/database
# 这个URL会被env.py中的get_sync_database_url()函数覆盖
sqlalchemy.url = postgresql+psycopg2://echo_note:password@localhost:5432/echo_note


# 后处理钩子配置
# 用于在生成迁移脚本后自动执行代码格式化和检查
[post_write_hooks]
# 启用black代码格式化和ruff代码检查
hooks = black, ruff

# Black代码格式化配置
black.type = console_scripts
black.entrypoint = black
black.options = --line-length 88 --target-version py311 REVISION_SCRIPT_FILENAME

# Ruff代码检查和自动修复配置
ruff.type = console_scripts
ruff.entrypoint = ruff
ruff.options = check --fix --select I,F,E,W REVISION_SCRIPT_FILENAME

# 日志配置
# 提供详细的迁移过程日志，便于调试和监控
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = INFO
handlers = console
qualname =

[logger_sqlalchemy]
level = INFO
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(asctime)s %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %Y-%m-%d %H:%M:%S
