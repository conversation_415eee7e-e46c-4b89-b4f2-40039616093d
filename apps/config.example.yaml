# EchoNote 应用配置文件示例
# 复制此文件为 config.yaml 并根据实际情况修改配置

# 应用基本配置
app:
  name: "EchoNote"
  version: "0.1.0"
  description: "EchoNote API服务 - 支持向量数据库和图数据库"
  debug: false  # 生产环境设置为 false

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000

# 数据库配置 (PostgreSQL with pgvector and Apache AGE)
database:
  host: "localhost"
  port: 5432
  name: "echo_note"
  user: "echo_note"
  password: "your_secure_password_here"  # 请修改为安全密码
  echo: false  # 开发时可设置为 true 查看SQL语句
  pool_size: 10
  max_overflow: 20
  
  # 测试数据库配置
  test_name: "echo_note_test"

# Redis配置 (with password authentication)
redis:
  host: "localhost"
  port: 6379  # 标准Redis端口
  db: 0
  password: "your_redis_password_here"  # 请修改为安全密码
  max_connections: 10

# JWT配置
jwt:
  secret_key: "your-very-secure-secret-key-at-least-32-characters-long"  # 请修改为安全密钥
  algorithm: "HS256"
  access_token_expire_minutes: 30

# CORS配置
cors:
  allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
    - "http://127.0.0.1:3000"
    - "http://127.0.0.1:8080"
    - "http://localhost:5173"  # Vite默认端口
    - "http://127.0.0.1:5173"
    # 生产环境请添加实际域名
    # - "https://yourdomain.com"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "*"

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  log_dir: "logs"
  max_file_size: 10485760  # 10MB
  backup_count: 5
  enable_file_logging: true
  enable_console_logging: true

# 文件上传配置
upload:
  max_file_size: 10485760  # 10MB
  upload_dir: "uploads"

# 向量数据库配置 (pgvector)
vector:
  dimension: 1536  # OpenAI embedding dimension
  index_type: "hnsw"  # HNSW or IVFFlat
  distance_metric: "cosine"  # cosine, l2, inner_product

# 图数据库配置 (Apache AGE)
graph:
  name: "echo_note_graph"
  age_schema: "ag_catalog"

# Docker配置
docker:
  network: "echo_note-network"
  postgres_container: "echo_note-postgres-dev"
  redis_container: "echo_note-redis-dev"
