#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:30
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : settings.py
# @Update  : 2025/8/7 11:30 YAML配置文件加载

from pathlib import Path
from typing import Any, Dict, List

import yaml
from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class AppConfig(BaseModel):
    """应用配置"""
    name: str = "EchoNote"
    version: str = "0.1.0"
    description: str = "EchoNote API服务"
    debug: bool = False


class ServerConfig(BaseModel):
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 8000


class DatabaseConfig(BaseModel):
    """数据库配置"""
    host: str = "localhost"
    port: int = 5432
    name: str = "echo_note"
    user: str = "echo_note"
    password: str = "password"
    echo: bool = False
    pool_size: int = 10
    max_overflow: int = 20
    test_name: str = "echo_note_test"


class RedisConfig(BaseModel):
    """Redis配置"""
    host: str = "localhost"
    port: int = 6380
    db: int = 0
    password: str = "password"
    max_connections: int = 10


class JWTConfig(BaseModel):
    """JWT配置"""
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30


class CORSConfig(BaseModel):
    """CORS配置"""
    allowed_origins: List[str] = ["http://localhost:3000"]
    allowed_methods: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: List[str] = ["*"]


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_dir: str = "logs"
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5
    enable_file_logging: bool = True
    enable_console_logging: bool = True

    @classmethod
    @field_validator('level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是: {", ".join(valid_levels)}')
        return v.upper()


class UploadConfig(BaseModel):
    """文件上传配置"""
    max_file_size: int = 10485760  # 10MB
    upload_dir: str = "uploads"


class VectorConfig(BaseModel):
    """向量数据库配置"""
    dimension: int = 1536
    index_type: str = "hnsw"
    distance_metric: str = "cosine"


class GraphConfig(BaseModel):
    """图数据库配置"""
    name: str = "echo_note_graph"
    age_schema: str = "ag_catalog"


class DockerConfig(BaseModel):
    """Docker配置"""
    network: str = "echo_note-network"
    postgres_container: str = "echo_note-postgres-dev"
    redis_container: str = "echo_note-redis-dev"


class Settings(BaseSettings):
    """应用配置类"""

    model_config = SettingsConfigDict(
        case_sensitive=False,
        extra='ignore',
        env_nested_delimiter='__',
        env_prefix='ECHONOTE_'
    )

    app: AppConfig = Field(default_factory=AppConfig)
    server: ServerConfig = Field(default_factory=ServerConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    jwt: JWTConfig = Field(default_factory=JWTConfig)
    cors: CORSConfig = Field(default_factory=CORSConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    upload: UploadConfig = Field(default_factory=UploadConfig)
    vector: VectorConfig = Field(default_factory=VectorConfig)
    graph: GraphConfig = Field(default_factory=GraphConfig)
    docker: DockerConfig = Field(default_factory=DockerConfig)

    @classmethod
    def load_from_yaml(cls, config_path: str = None) -> "Settings":
        """从YAML文件加载配置"""
        if config_path is None:
            # 默认配置文件路径
            config_path = Path(__file__).parent.parent / "config.yaml"

        config_path = Path(config_path)

        # 如果配置文件不存在，使用默认配置
        if not config_path.exists():
            print(f"⚠️ 配置文件不存在: {config_path}")
            print("使用默认配置")
            return cls()

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            if not config_data:
                print("⚠️ 配置文件为空，使用默认配置")
                return cls()

            print(f"✅ 成功加载配置文件: {config_path}")

            # 创建各个配置对象
            app_config = AppConfig(**(config_data.get('fast_app', {})))
            server_config = ServerConfig(**(config_data.get('server', {})))
            database_config = DatabaseConfig(**(config_data.get('database', {})))
            redis_config = RedisConfig(**(config_data.get('redis', {})))
            jwt_config = JWTConfig(**(config_data.get('jwt', {})))
            cors_config = CORSConfig(**(config_data.get('cors', {})))
            logging_config = LoggingConfig(**(config_data.get('logging', {})))
            upload_config = UploadConfig(**(config_data.get('upload', {})))
            vector_config = VectorConfig(**(config_data.get('vector', {})))
            graph_config = GraphConfig(**(config_data.get('graph', {})))
            docker_config = DockerConfig(**(config_data.get('docker', {})))

            return cls(
                app=app_config,
                server=server_config,
                database=database_config,
                redis=redis_config,
                jwt=jwt_config,
                cors=cors_config,
                logging=logging_config,
                upload=upload_config,
                vector=vector_config,
                graph=graph_config,
                docker=docker_config
            )

        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            print("使用默认配置")
            return cls()

    def setup_logging(self):
        """设置日志配置"""
        from .logging_config import setup_logging

        log_config = {
            'level':                  self.logging.level,
            'format':                 self.logging.format,
            'max_file_size':          self.logging.max_file_size,
            'backup_count':           self.logging.backup_count,
            'enable_file_logging':    self.logging.enable_file_logging,
            'enable_console_logging': self.logging.enable_console_logging
        }

        setup_logging(log_config, self.logging.log_dir)

    @classmethod
    @field_validator('jwt')
    def validate_jwt_secret(cls, v):
        """验证JWT密钥"""
        if len(v.secret_key) < 32:
            print("⚠️ 警告：JWT密钥长度建议至少32个字符")
        if v.secret_key == "your-secret-key-here":
            print("⚠️ 警告：请修改默认的JWT密钥")
        return v

    # 便捷属性，保持向后兼容
    @property
    def app_name(self) -> str:
        return self.app.name

    @property
    def app_version(self) -> str:
        return self.app.version

    @property
    def app_description(self) -> str:
        return self.app.description

    @property
    def debug(self) -> bool:
        return self.app.debug

    @property
    def host(self) -> str:
        return self.server.host

    @property
    def port(self) -> int:
        return self.server.port

    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return (
            f"postgresql+asyncpg://{self.database.user}:"
            f"{self.database.password}@{self.database.host}:"
            f"{self.database.port}/{self.database.name}"
        )

    @property
    def test_database_url(self) -> str:
        """构建测试数据库连接URL"""
        return (
            f"postgresql+asyncpg://{self.database.user}:"
            f"{self.database.password}@{self.database.host}:"
            f"{self.database.port}/{self.database.test_name}"
        )

    @property
    def redis_url(self) -> str:
        """构建Redis连接URL"""
        password_part = ""
        if self.redis.password:
            password_part = f":{self.redis.password}@"

        return (
            f"redis://{password_part}{self.redis.host}:"
            f"{self.redis.port}/{self.redis.db}"
        )

    # 向后兼容的属性
    @property
    def secret_key(self) -> str:
        return self.jwt.secret_key

    @property
    def algorithm(self) -> str:
        return self.jwt.algorithm

    @property
    def access_token_expire_minutes(self) -> int:
        return self.jwt.access_token_expire_minutes

    @property
    def allowed_origins(self) -> List[str]:
        return self.cors.allowed_origins

    @property
    def allowed_methods(self) -> List[str]:
        return self.cors.allowed_methods

    @property
    def allowed_headers(self) -> List[str]:
        return self.cors.allowed_headers

    @property
    def log_level(self) -> str:
        return self.logging.level

    @property
    def log_format(self) -> str:
        return self.logging.format

    @property
    def max_file_size(self) -> int:
        return self.upload.max_file_size

    @property
    def upload_dir(self) -> str:
        return self.upload.upload_dir

    @property
    def vector_dimension(self) -> int:
        return self.vector.dimension

    @property
    def vector_index_type(self) -> str:
        return self.vector.index_type

    @property
    def vector_distance_metric(self) -> str:
        return self.vector.distance_metric

    @property
    def graph_name(self) -> str:
        return self.graph.name

    @property
    def age_schema(self) -> str:
        return self.graph.age_schema

    @property
    def redis_max_connections(self) -> int:
        return self.redis.max_connections

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()

    def reload_logging(self):
        """重新加载日志配置"""
        self.setup_logging()


# 创建全局配置实例
settings = Settings.load_from_yaml()

# 自动设置日志
settings.setup_logging()
