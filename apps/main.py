#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 15:37
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : main.py
# @Update  : 2025/8/7 18:00 优化的FastAPI应用主文件

"""
EchoNote FastAPI应用主文件

该文件是EchoNote项目的FastAPI应用入口点，遵循Context7和FastAPI最佳实践：
- 现代化的应用配置和中间件设置
- 完整的CORS和安全配置
- 统一的异常处理和日志记录
- 生产就绪的应用生命周期管理
- 详细的API文档配置

应用特性：
- JWT认证系统
- 用户管理API
- 自动API文档生成
- 健康检查端点
- 错误处理和日志记录
- CORS支持
- 安全中间件

参考：FastAPI最佳实践和生产部署指南
"""

from __future__ import annotations

import logging
import sys
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Coroutine, Dict

from fastapi import FastAPI, HTTPException, Request, status
from fastapi.exception_handlers import (
    http_exception_handler,
    request_validation_exception_handler,
)
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.middleware.sessions import SessionMiddleware
from starlette.responses import Response

from apis import main_router
from configs import settings
from services.base import ServiceError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        # logging.FileHandler("logs/fast_app.log", encoding="utf-8")  # 需要确保logs目录存在
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    
    处理应用启动和关闭时的资源管理，包括：
    - 数据库连接初始化和清理
    - 缓存系统启动和关闭
    - 外部服务连接管理
    - 日志和监控系统初始化
    
    Args:
        app: FastAPI应用实例
        
    Yields:
        None: 应用运行期间
    """
    # 启动时执行
    logger.info("🚀 EchoNote应用启动中...")
    
    try:
        # 初始化数据库连接
        logger.info("📊 初始化数据库连接...")
        # 这里可以添加数据库连接初始化代码
        
        # 初始化缓存系统
        logger.info("🗄️ 初始化缓存系统...")
        # 这里可以添加Redis等缓存系统初始化代码
        
        # 初始化外部服务连接
        logger.info("🔗 初始化外部服务连接...")
        # 这里可以添加外部API客户端初始化代码
        
        logger.info("✅ EchoNote应用启动完成")
        
        yield  # 应用运行期间
        
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {str(e)}")
        raise
    finally:
        # 关闭时执行
        logger.info("🛑 EchoNote应用关闭中...")
        
        try:
            # 清理数据库连接
            logger.info("📊 清理数据库连接...")
            # 这里可以添加数据库连接清理代码
            
            # 清理缓存系统
            logger.info("🗄️ 清理缓存系统...")
            # 这里可以添加缓存系统清理代码
            
            # 清理外部服务连接
            logger.info("🔗 清理外部服务连接...")
            # 这里可以添加外部服务连接清理代码
            
            logger.info("✅ EchoNote应用关闭完成")
            
        except Exception as e:
            logger.error(f"❌ 应用关闭时发生错误: {str(e)}")


def create_app() -> FastAPI:
    """
    创建和配置FastAPI应用实例
    
    Returns:
        FastAPI: 配置完成的应用实例
    """
    # 应用配置
    app_configs = {
        "title": "EchoNote API",
        "description": """
        EchoNote项目的RESTful API服务
        
        ## 主要功能
        
        * **用户管理** - 完整的用户注册、登录、信息管理
        * **JWT认证** - 安全的令牌认证系统
        * **密码安全** - bcrypt哈希和密码强度验证
        * **API文档** - 自动生成的交互式API文档
        
        ## 认证方式
        
        使用JWT Bearer Token进行认证：
        ```
        Authorization: Bearer <your-token>
        ```
        
        ## 快速开始
        
        1. 创建用户账户：`POST /api/v1/users/`
        2. 用户登录：`POST /api/v1/auth/login`
        3. 使用返回的token访问受保护的API
        
        ## 技术栈
        
        - **FastAPI** - 现代化的Python Web框架
        - **SQLAlchemy** - 强大的ORM框架
        - **Pydantic** - 数据验证和序列化
        - **JWT** - 安全的令牌认证
        - **bcrypt** - 密码安全哈希
        """,
        "version": "1.0.0",
        "contact": {
            "name": "EchoNote Team",
            "email": "<EMAIL>",
        },
        "license_info": {
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
        "lifespan": lifespan,
    }
    
    # 根据环境配置文档显示
    # 检查是否应该显示API文档
    # 优先级：环境变量 > 配置文件 > 默认值
    import os
    show_docs = os.getenv("SHOW_DOCS", "true").lower() in ("true", "1", "yes", "on")

    # 如果明确设置不显示文档，则隐藏
    if not show_docs:
        app_configs["openapi_url"] = None  # 隐藏文档
        app_configs["docs_url"] = None
        app_configs["redoc_url"] = None
    
    # 创建应用实例
    app = FastAPI(**app_configs)
    
    return app


def setup_middleware(app: FastAPI) -> None:
    """
    配置应用中间件
    
    Args:
        app: FastAPI应用实例
    """
    # 1. 会话中间件（需要在CORS之前）
    app.add_middleware(
        SessionMiddleware,
        secret_key=settings.jwt.secret_key,
        max_age=86400,  # 24小时
        same_site="lax",
        https_only=not settings.app.debug  # 非调试模式使用HTTPS
    )
    
    # 2. 受信任主机中间件
    if not settings.app.debug:  # 生产环境（非调试模式）
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts= ["*"]
        )
    
    # 3. CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors.allowed_origins,
        allow_credentials=True,
        allow_methods=settings.cors.allowed_methods,
        allow_headers=settings.cors.allowed_headers,
        expose_headers=["*"],
        max_age=3600,  # 1小时
    )
    
    # 4. GZip压缩中间件
    app.add_middleware(
        GZipMiddleware,
        minimum_size=1000,  # 只压缩大于1KB的响应
        compresslevel=6     # 平衡压缩率和性能
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """
    配置全局异常处理器

    Args:
        app: FastAPI应用实例
    """

    @app.exception_handler(ServiceError)
    async def service_exception_handler(request: Request, exc: ServiceError) -> JSONResponse:
        """
        处理服务层异常

        Args:
            request: 请求对象
            exc: 服务异常

        Returns:
            JSONResponse: 错误响应
        """
        logger.error(
            f"Service error: {exc.message} | "
            f"Code: {exc.code} | "
            f"Details: {exc.details} | "
            f"URL: {request.url} | "
            f"Method: {request.method}"
        )

        # 根据异常代码确定HTTP状态码
        status_code_map = {
            "NOT_FOUND": status.HTTP_404_NOT_FOUND,
            "CONFLICT": status.HTTP_409_CONFLICT,
            "VALIDATION_ERROR": status.HTTP_400_BAD_REQUEST,
            "SERVICE_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        }

        status_code = status_code_map.get(exc.code, status.HTTP_500_INTERNAL_SERVER_ERROR)

        return JSONResponse(
            status_code=status_code,
            content={
                "error": {
                    "message": exc.message,
                    "code": exc.code,
                    "details": exc.details,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            }
        )

    @app.exception_handler(StarletteHTTPException)
    async def custom_http_exception_handler(
        request: Request,
        exc: StarletteHTTPException
    ) -> Response:
        """
        处理HTTP异常

        Args:
            request: 请求对象
            exc: HTTP异常

        Returns:
            JSONResponse: 错误响应
        """
        logger.warning(
            f"HTTP exception: {exc.detail} | "
            f"Status: {exc.status_code} | "
            f"URL: {request.url} | "
            f"Method: {request.method}"
        )

        # 复用默认处理器但添加自定义日志
        return await http_exception_handler(request, exc)

    @app.exception_handler(RequestValidationError)
    async def custom_validation_exception_handler(
        request: Request,
        exc: RequestValidationError
    ) -> JSONResponse:
        """
        处理请求验证异常

        Args:
            request: 请求对象
            exc: 验证异常

        Returns:
            JSONResponse: 错误响应
        """
        logger.warning(
            f"Validation error: {exc.errors()} | "
            f"URL: {request.url} | "
            f"Method: {request.method}"
        )

        # 复用默认处理器但添加自定义日志
        return await request_validation_exception_handler(request, exc)

    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        """
        处理未捕获的异常

        Args:
            request: 请求对象
            exc: 异常

        Returns:
            JSONResponse: 错误响应
        """
        logger.error(
            f"Unhandled exception: {str(exc)} | "
            f"Type: {type(exc).__name__} | "
            f"URL: {request.url} | "
            f"Method: {request.method}",
            exc_info=True
        )

        # 生产环境不暴露详细错误信息
        if not settings.fast_app.debug:  # 生产环境（非调试模式）
            detail = "内部服务器错误"
        else:
            detail = f"未处理的异常: {str(exc)}"

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error": {
                    "message": detail,
                    "code": "INTERNAL_SERVER_ERROR",
                    "timestamp": datetime.utcnow().isoformat(),
                }
            }
        )


def setup_routes(app: FastAPI) -> None:
    """
    配置应用路由

    Args:
        app: FastAPI应用实例
    """
    # 包含主路由器
    app.include_router(main_router)


def create_application() -> FastAPI:
    """
    创建完整配置的FastAPI应用

    Returns:
        FastAPI: 完全配置的应用实例
    """
    # 创建应用
    app = create_app()

    # 配置中间件
    setup_middleware(app)

    # 配置异常处理器
    setup_exception_handlers(app)

    # 配置路由
    setup_routes(app)

    logger.info("🎉 FastAPI应用配置完成")

    return app


# 创建应用实例
fast_app = create_application()


# 开发环境下的调试信息
if settings.app.debug:  # 调试模式
    logger.info("🔧 开发模式已启用")
    logger.info(f"📚 API文档: http://localhost:8000/docs")
    logger.info(f"📖 ReDoc文档: http://localhost:8000/redoc")
    logger.info(f"🔍 健康检查: http://localhost:8000/api/v1/health")


# 应用元数据（用于监控和部署）
fast_app.state.metadata = {
    "name": "EchoNote API",
    "version": "1.0.0",
    "environment": "development" if settings.app.debug else "production",
    "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
    "features": [
        "JWT认证",
        "用户管理",
        "密码安全",
        "API文档",
        "健康检查",
        "CORS支持",
        "GZip压缩",
        "异常处理",
        "日志记录",
    ]
}


if __name__ == "__main__":
    """
    开发服务器启动入口

    注意：生产环境请使用ASGI服务器（如uvicorn、gunicorn）
    """
    import uvicorn

    logger.info("🚀 启动开发服务器...")

    uvicorn.run(
        "main:fast_app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True,
        reload_dirs=["apps"],
        reload_includes=["*.py"],
    )
