#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 16:30
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : users.py
# @Update  : 2025/8/7 16:30 用户相关数据结构

"""
用户相关的Pydantic数据结构模块

该模块定义了用户相关操作的所有数据结构，包括：
- 用户创建、更新、查询的请求和响应模型
- 数据验证和序列化规则
- API接口的输入输出格式定义

使用Pydantic 2.0的现代化特性，提供完整的类型注解和验证支持。
"""

from __future__ import annotations

import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, EmailStr, Field, field_validator


class UserBase(BaseModel):
    """
    用户基础数据结构
    
    包含用户的基本信息字段，作为其他用户相关模型的基类。
    定义了所有用户模型共享的字段和验证规则。
    
    Attributes:
        username: 用户名，长度3-20个字符，只能包含字母、数字和下划线
        email: 用户邮箱地址，必须是有效的邮箱格式
        comment: 用户备注信息，可选字段，最大长度255个字符
    """

    username: str = Field(
        ...,
        min_length=3,
        max_length=20,
        pattern=r"^[a-zA-Z0-9_]+$",
        description="用户名，3-20个字符，只能包含字母、数字和下划线",
        examples=["john_doe", "user123", "admin_user"]
    )

    email: EmailStr = Field(
        ...,
        description="用户邮箱地址，必须是有效的邮箱格式",
        examples=["<EMAIL>", "<EMAIL>"]
    )

    comment: Optional[str] = Field(
        None,
        max_length=255,
        description="用户备注信息，可选字段",
        examples=["VIP用户", "测试账户", "管理员账户"]
    )

    @classmethod
    @field_validator('username')
    def validate_username(cls, v: str) -> str:
        """
        验证用户名格式
        
        Args:
            v: 用户名字符串
            
        Returns:
            str: 验证后的用户名
            
        Raises:
            ValueError: 用户名格式不正确时抛出
        """
        if not v:
            raise ValueError("用户名不能为空")

        # 检查是否包含非法字符
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError("用户名只能包含字母、数字、下划线和连字符")

        # 检查是否以数字开头
        if v[0].isdigit():
            raise ValueError("用户名不能以数字开头")

        return v.lower()  # 统一转换为小写

    @classmethod
    @field_validator('comment')
    def validate_comment(cls, v: Optional[str]) -> Optional[str]:
        """
        验证备注信息
        
        Args:
            v: 备注信息字符串
            
        Returns:
            Optional[str]: 验证后的备注信息
        """
        if v is not None:
            # 去除首尾空白字符
            v = v.strip()
            # 如果去除空白后为空字符串，则返回None
            if not v:
                return None
        return v


class UserCreate(UserBase):
    """
    用户创建请求数据结构
    
    用于API接口接收用户注册或创建用户的请求数据。
    继承自UserBase，并添加了密码字段和相关验证。
    
    Attributes:
        password: 用户密码，明文传输，服务端会进行哈希处理
        confirm_password: 确认密码，必须与password字段一致
    """

    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="用户密码，至少8个字符，最多128个字符",
        examples=["password123", "MySecurePass@2024"]
    )

    confirm_password: str = Field(
        ...,
        description="确认密码，必须与密码字段一致",
        examples=["password123", "MySecurePass@2024"]
    )

    @classmethod
    @field_validator('password')
    def validate_password(cls, v: str) -> str:
        """
        验证密码强度
        
        Args:
            v: 密码字符串
            
        Returns:
            str: 验证后的密码
            
        Raises:
            ValueError: 密码不符合要求时抛出
        """
        if len(v) < 8:
            raise ValueError("密码长度至少8个字符")

        # 检查密码复杂度
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)

        complexity_count = sum([has_upper, has_lower, has_digit, has_special])

        if complexity_count < 2:
            raise ValueError("密码必须包含至少两种类型的字符（大写字母、小写字母、数字、特殊字符）")

        return v

    @classmethod
    @field_validator('confirm_password')
    def validate_confirm_password(cls, v: str, info) -> str:
        """
        验证确认密码
        
        Args:
            v: 确认密码字符串
            info: 验证上下文信息
            
        Returns:
            str: 验证后的确认密码
            
        Raises:
            ValueError: 确认密码与密码不一致时抛出
        """
        if 'password' in info.data and v != info.data['password']:
            raise ValueError("确认密码与密码不一致")
        return v


class UserUpdate(BaseModel):
    """
    用户更新请求数据结构
    
    用于API接口接收用户信息更新的请求数据。
    所有字段都是可选的，只更新提供的字段。
    
    Attributes:
        username: 新用户名，可选
        email: 新邮箱地址，可选
        comment: 新备注信息，可选
        password: 新密码，可选
    """

    username: Optional[str] = Field(
        None,
        min_length=3,
        max_length=20,
        pattern=r"^[a-zA-Z0-9_]+$",
        description="新用户名，可选字段",
        examples=["new_username", "updated_user"]
    )

    email: Optional[EmailStr] = Field(
        None,
        description="新邮箱地址，可选字段",
        examples=["<EMAIL>"]
    )

    comment: Optional[str] = Field(
        None,
        max_length=255,
        description="新备注信息，可选字段",
        examples=["更新后的备注"]
    )

    password: Optional[str] = Field(
        None,
        min_length=8,
        max_length=128,
        description="新密码，可选字段",
        examples=["NewPassword@2024"]
    )

    # 复用UserBase和UserCreate的验证器
    _validate_username = field_validator('username')(UserBase.validate_username)
    _validate_comment = field_validator('comment')(UserBase.validate_comment)
    _validate_password = field_validator('password')(UserCreate.validate_password)


class UserResponse(UserBase):
    """
    用户响应数据结构
    
    用于API接口返回用户信息的响应数据。
    包含用户的所有公开信息，但不包含敏感信息如密码。
    
    Attributes:
        id: 用户唯一标识符，UUID格式
        created_at: 用户创建时间
        updated_at: 用户最后更新时间
    """

    model_config = ConfigDict(
        from_attributes=True,  # 允许从SQLAlchemy模型创建
        json_encoders={
            datetime:  lambda v: v.isoformat(),  # 自定义datetime序列化
            uuid.UUID: lambda v: str(v),  # 自定义UUID序列化
        }
    )

    id: uuid.UUID = Field(
        ...,
        description="用户唯一标识符",
        examples=["550e8400-e29b-41d4-a716-************"]
    )

    created_at: datetime = Field(
        ...,
        description="用户创建时间",
        examples=["2024-01-01T12:00:00"]
    )

    updated_at: datetime = Field(
        ...,
        description="用户最后更新时间",
        examples=["2024-01-01T12:00:00"]
    )


class UserListResponse(BaseModel):
    """
    用户列表响应数据结构
    
    用于API接口返回用户列表的响应数据。
    包含分页信息和用户列表。
    
    Attributes:
        users: 用户列表
        total: 用户总数
        page: 当前页码
        size: 每页大小
        pages: 总页数
    """

    users: list[UserResponse] = Field(
        ...,
        description="用户列表"
    )

    total: int = Field(
        ...,
        ge=0,
        description="用户总数",
        examples=[100, 0]
    )

    page: int = Field(
        ...,
        ge=1,
        description="当前页码，从1开始",
        examples=[1, 2, 10]
    )

    size: int = Field(
        ...,
        ge=1,
        le=100,
        description="每页大小，最大100",
        examples=[10, 20, 50]
    )

    pages: int = Field(
        ...,
        ge=0,
        description="总页数",
        examples=[10, 0]
    )


class UserQuery(BaseModel):
    """
    用户查询参数数据结构
    
    用于API接口接收用户查询的请求参数。
    支持分页、排序、筛选等功能。
    
    Attributes:
        page: 页码，从1开始
        size: 每页大小
        username: 用户名筛选，支持模糊匹配
        email: 邮箱筛选，支持模糊匹配
        sort_by: 排序字段
        sort_order: 排序方向
    """

    page: int = Field(
        1,
        ge=1,
        description="页码，从1开始",
        examples=[1, 2, 10]
    )

    size: int = Field(
        10,
        ge=1,
        le=100,
        description="每页大小，最大100",
        examples=[10, 20, 50]
    )

    username: Optional[str] = Field(
        None,
        max_length=20,
        description="用户名筛选，支持模糊匹配",
        examples=["john", "admin"]
    )

    email: Optional[str] = Field(
        None,
        max_length=50,
        description="邮箱筛选，支持模糊匹配",
        examples=["@example.com", "john"]
    )

    sort_by: str = Field(
        "created_at",
        description="排序字段",
        examples=["created_at", "updated_at", "username", "email"]
    )

    sort_order: str = Field(
        "desc",
        pattern=r"^(asc|desc)$",
        description="排序方向，asc或desc",
        examples=["asc", "desc"]
    )

    @classmethod
    @field_validator('sort_by')
    def validate_sort_by(cls, v: str) -> str:
        """
        验证排序字段
        
        Args:
            v: 排序字段名
            
        Returns:
            str: 验证后的排序字段名
            
        Raises:
            ValueError: 排序字段不支持时抛出
        """
        allowed_fields = ["id", "username", "email", "created_at", "updated_at"]
        if v not in allowed_fields:
            raise ValueError(f"排序字段必须是以下之一: {', '.join(allowed_fields)}")
        return v


class UserLogin(BaseModel):
    """
    用户登录请求数据结构
    
    用于API接口接收用户登录的请求数据。
    支持用户名或邮箱登录。
    
    Attributes:
        identifier: 登录标识符，可以是用户名或邮箱
        password: 登录密码
    """

    identifier: str = Field(
        ...,
        min_length=3,
        max_length=50,
        description="登录标识符，可以是用户名或邮箱",
        examples=["john_doe", "<EMAIL>"]
    )

    password: str = Field(
        ...,
        min_length=1,
        max_length=128,
        description="登录密码",
        examples=["password123"]
    )


class UserLoginResponse(BaseModel):
    """
    用户登录响应数据结构

    用于API接口返回用户登录成功的响应数据。
    包含访问令牌、刷新令牌和用户基本信息。

    Attributes:
        access_token: JWT访问令牌
        refresh_token: JWT刷新令牌
        token_type: 令牌类型，通常为"bearer"
        expires_in: 令牌过期时间（秒）
        user: 用户基本信息
    """

    access_token: str = Field(
        ...,
        description="JWT访问令牌",
        examples=["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."]
    )

    refresh_token: str = Field(
        ...,
        description="JWT刷新令牌",
        examples=["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."]
    )

    token_type: str = Field(
        "bearer",
        description="令牌类型",
        examples=["bearer"]
    )

    expires_in: int = Field(
        ...,
        gt=0,
        description="令牌过期时间（秒）",
        examples=[3600, 7200]
    )

    user: UserResponse = Field(
        ...,
        description="用户基本信息"
    )
