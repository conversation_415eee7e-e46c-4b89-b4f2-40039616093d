"""${message}

迁移ID: ${up_revision}
父迁移: ${down_revision | repr}
分支标签: ${branch_labels | repr}
依赖: ${depends_on | repr}
创建时间: ${create_date}

"""

# 迁移标识符，由Alembic使用
# 这些变量用于构建迁移依赖图和版本控制
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}

from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# 导入自定义类型和工具函数
from typing import Sequence, Union
from alembic import context


def get_context() -> dict:
    """
    获取迁移上下文信息
    
    Returns:
        dict: 包含上下文信息的字典
    """
    return context.get_x_argument(as_dictionary=True)


def upgrade() -> None:
    """
    升级数据库结构
    
    该函数包含将数据库从当前版本升级到新版本所需的所有操作。
    包括创建表、添加列、创建索引、修改约束等操作。
    
    注意：
    - 所有操作都应该是可逆的
    - 考虑数据迁移的性能影响
    - 在生产环境中谨慎处理大表的结构变更
    """
    # 检查是否有特殊的上下文参数
    ctx = get_context()
    
    # 可以根据上下文参数执行不同的迁移逻辑
    # 例如：只执行结构迁移或同时执行数据迁移
    if ctx.get('schema_only', False):
        # 只执行结构变更
        schema_upgrades()
    elif ctx.get('data_only', False):
        # 只执行数据迁移
        data_upgrades()
    else:
        # 默认：先执行结构变更，再执行数据迁移
        schema_upgrades()
        data_upgrades()


def downgrade() -> None:
    """
    降级数据库结构
    
    该函数包含将数据库从当前版本降级到上一版本所需的所有操作。
    这是upgrade()函数的逆操作。
    
    注意：
    - 降级操作可能导致数据丢失
    - 在生产环境中应谨慎使用
    - 确保所有操作都经过充分测试
    """
    # 检查是否有特殊的上下文参数
    ctx = get_context()
    
    # 降级时通常先回滚数据变更，再回滚结构变更
    if ctx.get('schema_only', False):
        # 只回滚结构变更
        schema_downgrades()
    elif ctx.get('data_only', False):
        # 只回滚数据迁移
        data_downgrades()
    else:
        # 默认：先回滚数据迁移，再回滚结构变更
        data_downgrades()
        schema_downgrades()


def schema_upgrades() -> None:
    """
    数据库结构升级操作
    
    包含所有DDL操作：
    - 创建/删除表
    - 添加/删除列
    - 创建/删除索引
    - 修改约束
    - 修改数据类型
    """
    ${upgrades if upgrades else "pass"}


def schema_downgrades() -> None:
    """
    数据库结构降级操作
    
    schema_upgrades()的逆操作
    """
    ${downgrades if downgrades else "pass"}


def data_upgrades() -> None:
    """
    数据迁移升级操作
    
    包含所有DML操作：
    - 数据转换
    - 默认值填充
    - 数据清理
    - 引用完整性修复
    
    注意：
    - 大量数据操作应考虑分批处理
    - 在事务中执行以确保一致性
    - 提供进度反馈对于长时间运行的操作
    """
    # 示例：批量数据处理
    # connection = op.get_bind()
    # 
    # # 分批处理大量数据
    # batch_size = 1000
    # offset = 0
    # 
    # while True:
    #     result = connection.execute(
    #         sa.text("SELECT id FROM some_table LIMIT :limit OFFSET :offset"),
    #         {"limit": batch_size, "offset": offset}
    #     )
    #     rows = result.fetchall()
    #     
    #     if not rows:
    #         break
    #         
    #     # 处理当前批次的数据
    #     for row in rows:
    #         # 执行数据转换逻辑
    #         pass
    #     
    #     offset += batch_size
    #     print(f"已处理 {offset} 条记录...")
    
    pass


def data_downgrades() -> None:
    """
    数据迁移降级操作
    
    data_upgrades()的逆操作
    
    注意：
    - 数据降级可能导致信息丢失
    - 应该有备份策略
    - 考虑是否需要保留历史数据
    """
    pass


# 工具函数：可以在迁移脚本中使用的辅助函数

def table_exists(table_name: str, schema: str = None) -> bool:
    """
    检查表是否存在
    
    Args:
        table_name: 表名
        schema: 模式名（可选）
        
    Returns:
        bool: 表是否存在
    """
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    return inspector.has_table(table_name, schema=schema)


def column_exists(table_name: str, column_name: str, schema: str = None) -> bool:
    """
    检查列是否存在
    
    Args:
        table_name: 表名
        column_name: 列名
        schema: 模式名（可选）
        
    Returns:
        bool: 列是否存在
    """
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    
    if not inspector.has_table(table_name, schema=schema):
        return False
    
    columns = [col['name'] for col in inspector.get_columns(table_name, schema=schema)]
    return column_name in columns


def index_exists(index_name: str, table_name: str = None, schema: str = None) -> bool:
    """
    检查索引是否存在
    
    Args:
        index_name: 索引名
        table_name: 表名（可选）
        schema: 模式名（可选）
        
    Returns:
        bool: 索引是否存在
    """
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    
    if table_name:
        if not inspector.has_table(table_name, schema=schema):
            return False
        indexes = [idx['name'] for idx in inspector.get_indexes(table_name, schema=schema)]
        return index_name in indexes
    else:
        # 检查所有表的索引
        for table in inspector.get_table_names(schema=schema):
            indexes = [idx['name'] for idx in inspector.get_indexes(table, schema=schema)]
            if index_name in indexes:
                return True
        return False
