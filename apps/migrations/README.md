# EchoNote 数据库迁移指南

本目录包含EchoNote项目的Alembic数据库迁移配置和脚本。Alembic是SQLAlchemy的数据库迁移工具，用于管理数据库结构的版本控制。

## 📁 目录结构

```
migrations/
├── README.md           # 本文件，使用说明
├── env.py             # Alembic环境配置脚本
├── script.py.mako     # 迁移脚本模板
└── versions/          # 迁移脚本存放目录
    └── (迁移脚本文件)
```

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的依赖：

```bash
pip install alembic sqlalchemy psycopg2-binary
```

### 2. 配置数据库连接

编辑 `alembic.ini` 文件中的数据库连接URL：

```ini
sqlalchemy.url = postgresql://username:password@localhost:5432/database_name
```

或者使用环境变量：

```bash
export DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
```

### 3. 创建首次迁移

```bash
# 自动生成迁移脚本
alembic revision --autogenerate -m "初始化数据库结构"

# 执行迁移
alembic upgrade head
```

## 📋 常用命令

### 迁移管理

```bash
# 创建新的迁移脚本（手动）
alembic revision -m "描述信息"

# 自动生成迁移脚本（推荐）
alembic revision --autogenerate -m "描述信息"

# 执行迁移到最新版本
alembic upgrade head

# 执行迁移到指定版本
alembic upgrade <revision_id>

# 回滚到上一个版本
alembic downgrade -1

# 回滚到指定版本
alembic downgrade <revision_id>

# 回滚到初始状态
alembic downgrade base
```

### 版本查看

```bash
# 查看当前数据库版本
alembic current

# 查看迁移历史
alembic history

# 查看详细历史
alembic history --verbose

# 查看指定范围的历史
alembic history -r <start>:<end>
```

### 高级操作

```bash
# 生成SQL脚本（离线模式）
alembic upgrade head --sql

# 标记数据库为指定版本（不执行迁移）
alembic stamp head

# 使用自定义参数
alembic upgrade head -x schema_only=true
alembic upgrade head -x data_only=true
```

## 🔧 配置说明

### env.py 配置

`env.py` 文件包含以下重要配置：

- **target_metadata**: 指向项目的SQLAlchemy元数据
- **include_name**: 过滤器函数，控制哪些对象包含在迁移中
- **include_object**: 对象级过滤器
- **异步支持**: 支持异步数据库操作

### 自定义过滤器

可以通过修改 `include_name` 和 `include_object` 函数来自定义迁移行为：

```python
def include_name(name, type_, parent_names):
    # 排除临时表
    if type_ == "table" and name.startswith("temp_"):
        return False
    return True
```

## 📝 最佳实践

### 1. 迁移脚本编写

- **描述性命名**: 使用清晰的描述信息
- **原子操作**: 每个迁移应该是一个逻辑单元
- **可逆性**: 确保downgrade函数正确实现
- **测试**: 在开发环境中充分测试

### 2. 数据迁移

- **分批处理**: 大量数据操作应分批进行
- **性能考虑**: 避免在迁移中执行耗时操作
- **备份**: 重要数据变更前进行备份
- **监控**: 提供进度反馈

### 3. 生产环境

- **审核**: 生产迁移前进行代码审核
- **备份**: 执行前备份数据库
- **监控**: 监控迁移执行过程
- **回滚计划**: 准备回滚方案

## 🛠️ 故障排除

### 常见问题

1. **导入错误**
   ```bash
   # 确保Python路径正确
   export PYTHONPATH=/path/to/your/project:$PYTHONPATH
   ```

2. **连接错误**
   ```bash
   # 检查数据库连接配置
   alembic current  # 测试连接
   ```

3. **版本冲突**
   ```bash
   # 查看当前状态
   alembic current
   alembic history
   
   # 手动解决冲突
   alembic stamp <correct_revision>
   ```

### 调试模式

启用详细日志：

```bash
# 修改 alembic.ini 中的日志级别
[logger_alembic]
level = DEBUG
```

## 🔗 相关资源

- [Alembic官方文档](https://alembic.sqlalchemy.org/)
- [SQLAlchemy文档](https://docs.sqlalchemy.org/)
- [项目Wiki](链接到项目文档)

## 📞 支持

如有问题，请：

1. 查看本文档和官方文档
2. 检查项目Issue
3. 联系开发团队

---

**注意**: 在生产环境中执行迁移前，请务必备份数据库并在测试环境中验证迁移脚本。
