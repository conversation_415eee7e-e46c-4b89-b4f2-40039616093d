#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
Alembic环境配置脚本

该脚本配置Alembic的运行环境，包括数据库连接、元数据配置、
迁移上下文设置等。支持在线和离线两种迁移模式。

主要功能：
- 配置数据库连接和SQLAlchemy引擎
- 设置目标元数据用于自动生成迁移
- 配置迁移上下文和事务管理
- 支持异步数据库操作
- 提供自定义钩子和过滤器
"""

from __future__ import annotations

from logging.config import fileConfig
from typing import Any

from alembic import context
from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config

# 导入项目配置和模型
from configs import Base, settings

# 导入所有模型类，确保Alembic能够检测到它们
# 这是关键步骤：必须导入所有模型类，否则Alembic无法生成迁移

# Alembic配置对象，提供对alembic.ini文件的访问
config = context.config

# 配置日志系统
# 如果配置文件中有日志配置，则使用它来配置Python日志
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 设置目标元数据用于自动生成迁移
# 这是Alembic比较数据库当前状态和期望状态的基础
target_metadata = Base.metadata


# 其他值可以从配置中获取，例如：
# my_important_option = config.get_main_option("my_important_option")
# ... 等等


def get_sync_database_url() -> str:
    """
    获取同步数据库连接URL

    Alembic需要同步连接，而应用使用异步连接。
    这个函数将异步URL转换为同步URL。

    Returns:
        str: 同步数据库连接URL
    """
    # 从配置文件获取URL，如果没有则使用项目设置
    url = config.get_main_option("sqlalchemy.url")

    if url and url != "postgresql://postgres:password@localhost:5432/echonote":
        # 如果配置文件中有自定义URL，直接使用
        return url

    # 否则从项目设置构建同步URL
    return (
        f"postgresql+psycopg2://{settings.database.user}:"
        f"{settings.database.password}@{settings.database.host}:"
        f"{settings.database.port}/{settings.database.name}"
    )


def include_name(name: str | None, type_: str, parent_names: dict[str, str | None]) -> bool:
    """
    过滤器函数：决定哪些数据库对象应该包含在迁移中
    
    该函数在自动生成迁移时被调用，用于过滤表、索引、约束等对象。
    可以根据命名规则、模式等条件来决定是否包含特定对象。
    
    Args:
        name: 对象名称（表名、索引名等）
        type_: 对象类型（'table', 'index', 'unique_constraint'等）
        parent_names: 父对象名称字典
        
    Returns:
        bool: True表示包含该对象，False表示排除
        
    Example:
        # 排除临时表
        if type_ == "table" and name and name.startswith("temp_"):
            return False
        return True
    """
    # 排除Alembic版本表
    if type_ == "table" and name == "alembic_version":
        return False

    # 排除临时表和测试表
    if type_ == "table" and name and (name.startswith("temp_") or name.startswith("test_")):
        return False

    # 默认包含所有其他对象
    return True


def include_object(
    object_: Any, name: str | None, type_: str, reflected: bool, compare_to: Any
    ) -> bool:
    """
    对象级过滤器：决定是否包含特定的SQLAlchemy对象
    
    这是比include_name更细粒度的过滤器，可以访问完整的SQLAlchemy对象。
    
    Args:
        object_: SQLAlchemy对象（Table、Column等）
        name: 对象名称
        type_: 对象类型
        reflected: 是否为反射得到的对象
        compare_to: 比较目标对象
        
    Returns:
        bool: True表示包含该对象，False表示排除
    """
    # 可以在这里添加更复杂的过滤逻辑
    # 例如：基于对象属性、注释等进行过滤
    return True


def run_migrations_offline() -> None:
    """
    离线模式运行迁移

    在离线模式下，Alembic不会连接到数据库，而是生成SQL脚本。
    这种模式适用于：
    - 生成SQL脚本供DBA审核
    - 在无法直接连接数据库的环境中使用
    - 批量执行迁移脚本

    配置上下文时只需要数据库URL，不需要实际连接。
    """
    url = get_sync_database_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_name=include_name,
        include_object=include_object,
        compare_type=True,
        compare_server_default=True,
        render_as_batch=False,  # 如果使用SQLite，可能需要设置为True
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """
    执行迁移的核心函数
    
    该函数配置迁移上下文并执行实际的迁移操作。
    被在线和异步迁移模式共同使用。
    
    Args:
        connection: 数据库连接对象
    """
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        include_name=include_name,
        include_object=include_object,
        compare_type=True,
        compare_server_default=True,
        render_as_batch=False,
        # 可以添加更多配置选项
        # process_revision_directives=process_revision_directives,
        # render_item=render_item,
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """
    异步模式运行迁移
    
    使用SQLAlchemy的异步引擎执行迁移。
    适用于使用异步数据库驱动的应用。
    """
    connectable = async_engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """
    在线模式运行迁移

    在在线模式下，Alembic直接连接到数据库执行迁移。
    这是最常用的模式，支持：
    - 直接执行DDL语句
    - 事务管理
    - 错误回滚
    - 实时反馈

    支持同步和异步两种连接方式。
    """
    # 检查是否有外部提供的连接（用于程序化调用）
    connectable = config.attributes.get("connection", None)

    if connectable is None:
        # 没有外部连接，创建新的引擎
        from sqlalchemy import engine_from_config

        # 创建配置字典，使用同步数据库URL
        configuration = config.get_section(config.config_ini_section, {})
        configuration["sqlalchemy.url"] = get_sync_database_url()

        connectable = engine_from_config(
            configuration,
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
        )

        # 使用新创建的引擎连接
        with connectable.connect() as connection:
            do_run_migrations(connection)
    else:
        # 使用外部提供的连接
        do_run_migrations(connectable)


# 主执行逻辑
# 根据运行模式选择相应的迁移函数
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
