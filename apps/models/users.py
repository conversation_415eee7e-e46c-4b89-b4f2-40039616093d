#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 15:36
# <AUTHOR> la<PERSON>o
# @Email   : <EMAIL>
# @File    : users.py
# @Update  : 2025/8/7 15:36 更新描述

"""
用户模型模块

该模块定义了用户数据模型，包含用户的基本信息如用户名、密码和邮箱等。
使用SQLAlchemy 2.0的现代化声明式映射风格，提供完整的类型注解支持。
"""

from __future__ import annotations

from typing import Optional

from pydantic import EmailStr
from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column

from models.base_model import BaseModel


class Users(BaseModel):
    """
    用户数据模型

    该类定义了系统中用户的数据结构，继承自BaseModel，
    自动包含id、created_at、updated_at等基础字段。

    Attributes:
        username: 用户名，唯一标识符，长度限制为20个字符
        password: 用户密码的哈希值，长度限制为128个字符
        email: 用户邮箱地址，唯一标识符，长度限制为50个字符
        comment: 用户提交记录或备注信息，可选字段

    Table:
        users: 对应数据库中的users表

    Constraints:
        - username字段具有唯一性约束
        - email字段具有唯一性约束
        - username、password、email字段均不能为空

    Example:
        >>> user = Users(
        ...     username="john_doe",
        ...     password="hashed_password_here",
        ...     email=EmailStr("<EMAIL>"),
        ...     commit="用户注册"
        ... )
    """

    __tablename__ = "users"

    # 用户名：唯一标识符，不能为空，最大长度20个字符
    username: Mapped[str] = mapped_column(
        String(20),
        unique=True,
        nullable=False,
        comment="用户名，系统唯一标识符",
        doc="用户的登录用户名，必须唯一且不能为空"
    )

    # 密码：存储哈希后的密码，不能为空，最大长度128个字符
    password: Mapped[str] = mapped_column(
        String(128),
        nullable=False,
        comment="用户密码哈希值",
        doc="用户密码的哈希值，用于身份验证"
    )

    # 邮箱：用户邮箱地址，唯一标识符，不能为空，最大长度50个字符
    email: Mapped[EmailStr] = mapped_column(
        String(50),
        unique=True,
        nullable=False,
        comment="用户邮箱地址",
        doc="用户的邮箱地址，必须唯一且不能为空，用于通知和找回密码"
    )

    # 提交记录：用户操作备注或提交信息，可选字段，最大长度255个字符
    comment: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        default=None,
        comment="用户提交记录或备注信息",
        doc="记录用户的操作备注、提交信息或其他相关说明，可选字段"
    )

    def __repr__(self) -> str:
        """
        返回用户对象的字符串表示

        Returns:
            str: 包含用户ID、用户名和邮箱的字符串表示
        """
        return f"<Users(id={self.id!r}, username={self.username!r}, email={self.email!r})>"

    def __str__(self) -> str:
        """
        返回用户对象的友好字符串表示

        Returns:
            str: 用户名字符串
        """
        return self.username
