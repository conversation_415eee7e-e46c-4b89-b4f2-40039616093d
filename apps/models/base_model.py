#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 15:48
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : base_model.py
# @Update  : 2025/8/7 15:48 更新描述

"""
基础模型模块

该模块定义了SQLAlchemy ORM的基础模型类和混入类，提供了通用的数据库字段和功能。
使用SQLAlchemy 2.0的现代化声明式映射风格，包含完整的类型注解支持。

主要组件:
- UUIDPrimaryKeyMixin: 提供UUID主键字段的混入类
- TimestampMixin: 提供时间戳字段的混入类
- BaseModel: 组合了上述混入类的抽象基础模型类

所有继承自BaseModel的模型都将自动获得:
- id: UUID类型的主键
- created_at: 创建时间戳
- updated_at: 更新时间戳
"""

from __future__ import annotations

import uuid
from datetime import datetime
from typing import Any

from sqlalchemy import DateTime, String, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, declarative_mixin, mapped_column

from configs import Base


@declarative_mixin
class UUIDPrimaryKeyMixin:
    """
    UUID主键混入类

    为模型提供UUID类型的主键字段。UUID相比自增ID具有以下优势:
    - 全局唯一性，适合分布式系统
    - 不会泄露业务信息（如记录数量）
    - 更好的安全性

    Attributes:
        id: UUID类型的主键，自动生成，不可为空

    Example:
        >>> class MyModel(UUIDPrimaryKeyMixin, Base):
        ...     __tablename__ = "my_table"
        ...     name: Mapped[str]
    """

    # UUID主键：使用UUID4算法生成，确保全局唯一性
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        nullable=False,
        comment="主键ID，使用UUID4算法生成",
        doc="UUID类型的主键，自动生成，确保全局唯一性"
    )


@declarative_mixin
class TimestampMixin:
    """
    时间戳混入类

    为模型提供创建时间和更新时间字段，支持自动时间戳管理。
    使用数据库级别的默认值和触发器确保时间戳的准确性。

    Attributes:
        created_at: 记录创建时间，由数据库自动设置
        updated_at: 记录最后更新时间，每次更新时自动刷新

    Note:
        - created_at使用数据库的now()函数设置，确保时区一致性
        - updated_at在每次更新记录时自动刷新为当前时间
        - 所有时间戳都包含时区信息，建议使用UTC时区

    Example:
        >>> class MyModel(TimestampMixin, Base):
        ...     __tablename__ = "my_table"
        ...     name: Mapped[str]
        >>> # 创建记录时，created_at和updated_at会自动设置
        >>> # 更新记录时，updated_at会自动刷新
    """

    # 创建时间：记录首次创建的时间戳，由数据库自动设置
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="记录创建时间",
        doc="记录的创建时间戳，由数据库自动设置，包含时区信息"
    )

    # 更新时间：记录最后更新的时间戳，每次更新时自动刷新
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="记录最后更新时间",
        doc="记录的最后更新时间戳，每次更新时自动刷新，包含时区信息"
    )


@declarative_mixin
class CommentMixin:
    """
    注释混入类

    为模型提供注释字段，用于记录字段的描述信息。
    使用数据库级别的默认值和触发器确保注释的准确性。
    """
    comment: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment="字段注释",
        doc="字段的描述信息，用于记录字段的用途或约束"
    )


class BaseModel(UUIDPrimaryKeyMixin, TimestampMixin, CommentMixin, Base):
    """
    抽象基础模型类

    组合了UUID主键和时间戳功能的抽象基类，为所有业务模型提供统一的基础结构。
    继承自该类的模型将自动获得UUID主键和时间戳字段。

    Features:
        - UUID主键：全局唯一，适合分布式系统
        - 自动时间戳：创建和更新时间自动管理
        - 类型安全：完整的类型注解支持
        - 现代化语法：使用SQLAlchemy 2.0最新特性

    Attributes:
        id: UUID类型主键（继承自UUIDPrimaryKeyMixin）
        created_at: 创建时间戳（继承自TimestampMixin）
        updated_at: 更新时间戳（继承自TimestampMixin）

    Note:
        - 这是一个抽象类，不能直接实例化
        - 所有业务模型都应该继承自这个基类
        - 提供了统一的字段命名和类型约定

    Example:
        >>> from sqlalchemy import String
        >>> class User(BaseModel):
        ...     __tablename__ = "users"
        ...     username: Mapped[str] = mapped_column(String(50))
        ...     email: Mapped[str] = mapped_column(String(100))
        >>>
        >>> # User模型自动包含id、created_at、updated_at字段
        >>> user = User(username="john", email="<EMAIL>")
        >>> print(user.id)  # UUID对象
        >>> print(user.created_at)  # datetime对象
    """

    __abstract__ = True

    def __repr__(self) -> str:
        """
        返回模型对象的字符串表示

        提供统一的对象表示格式，包含类名和主键ID。
        子类可以重写此方法以提供更详细的信息。

        Returns:
            str: 包含类名和ID的字符串表示

        Example:
            >>> from models.users import Users
            >>> user = Users(username="john")
            >>> print(repr(user))
            <User(id=550e8400-e29b-41d4-a716-************)>
        """
        return f"<{self.__class__.__name__}(id={self.id!r})>"

    def to_dict(self, exclude: set[str] | None = None) -> dict[str, Any]:
        """
        将模型对象转换为字典

        将模型的所有字段转换为字典格式，便于序列化和API响应。
        可以通过exclude参数排除敏感字段。

        Args:
            exclude: 要排除的字段名集合，默认为None

        Returns:
            dict[str, Any]: 包含模型字段的字典

        Example:
            >>> from models.users import Users
            >>> from pydantic import EmailStr
            >>> user = Users(username="john", email=EmailStr("<EMAIL>"))
            >>> user_dict = user.to_dict()
            >>> print(user_dict)
            {
                'id': '550e8400-e29b-41d4-a716-************',
                'username': 'john',
                'email': '<EMAIL>',
                'created_at': datetime(...),
                'updated_at': datetime(...)
            }
            >>>
            >>> # 排除敏感字段
            >>> safe_dict = user.to_dict(exclude={'email'})
        """
        if exclude is None:
            exclude = set()

        result = {}
        for column in self.__table__.columns:
            field_name = column.name
            if field_name not in exclude:
                value = getattr(self, field_name)
                # 处理UUID和datetime的序列化
                if isinstance(value, uuid.UUID):
                    result[field_name] = str(value)
                elif isinstance(value, datetime):
                    result[field_name] = value.isoformat()
                else:
                    result[field_name] = value
        return result
