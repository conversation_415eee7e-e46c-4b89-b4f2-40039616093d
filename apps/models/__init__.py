#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 15:36
# <AUTHOR> la<PERSON>o
# @Email   : <EMAIL>
# @File    : __init__.py
# @Update  : 2025/8/7 15:36 更新描述

"""
模型模块

该模块包含所有SQLAlchemy数据模型的定义。
所有模型都继承自BaseModel，提供统一的基础功能。

导入所有模型类以确保：
1. Alembic能够检测到所有模型进行迁移
2. 应用启动时所有表结构都被注册
3. 模型之间的关系能够正确建立
"""

# 导入基础模型
from .base_model import BaseModel

# 导入所有业务模型
from .users import Users

# 导出所有模型，便于其他模块导入
__all__ = [
    # 基础模型
    "BaseModel",

    # 业务模型
    "Users",

    # 在这里添加新的模型类
    # "Posts",
    # "Comments",
    # "Categories",
]
