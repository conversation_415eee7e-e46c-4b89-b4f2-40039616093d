# EchoNote 服务层使用指南

本指南介绍如何使用EchoNote项目的服务层，包括用户服务和认证服务的使用方法。

## 📋 服务层架构

```
Controller Layer (API路由)
    ↓
Service Layer (业务逻辑)
    ↓
Model Layer (数据访问)
```

## 🏗️ 服务类结构

### BaseService

所有服务类的基类，提供通用的CRUD操作：

```python
from services import BaseService

class MyService(BaseService[MyModel, MyCreateSchema, MyUpdateSchema]):
    def __init__(self):
        super().__init__(MyModel)
```

### 异常处理

服务层定义了统一的异常类型：

- `ServiceError`: 基础服务异常
- `NotFoundError`: 资源未找到
- `ConflictError`: 资源冲突（如唯一约束违反）
- `ValidationError`: 数据验证失败

## 👤 用户服务使用

### 基本用法

```python
from services import user_service
from schemas.users import User<PERSON>reate, UserUpdate, UserQuery

# 创建用户
async def create_user_example(session):
    user_data = UserCreate(
        username="john_doe",
        email="<EMAIL>", 
        password="password123",
        confirm_password="password123",
        comment="新用户"
    )
    
    try:
        user = await user_service.create_user(session, user_data)
        print(f"用户创建成功: {user.username}")
        return user
    except ConflictError as e:
        print(f"创建失败: {e.message}")
        return None

# 获取用户
async def get_user_example(session, user_id):
    user = await user_service.get_user_by_id(session, user_id)
    if user:
        print(f"找到用户: {user.username}")
    else:
        print("用户不存在")
    return user

# 更新用户
async def update_user_example(session, user_id):
    update_data = UserUpdate(
        email="<EMAIL>",
        comment="更新后的用户"
    )
    
    try:
        user = await user_service.update_user(session, user_id, update_data)
        if user:
            print(f"用户更新成功: {user.email}")
        return user
    except ConflictError as e:
        print(f"更新失败: {e.message}")
        return None

# 查询用户列表
async def list_users_example(session):
    query = UserQuery(
        page=1,
        size=10,
        username="john",  # 模糊搜索
        sort_by="created_at",
        sort_order="desc"
    )
    
    result = await user_service.get_users(session, query)
    print(f"找到 {result.total} 个用户，当前第 {result.page} 页")
    
    for user in result.users:
        print(f"- {user.username} ({user.email})")
    
    return result

# 删除用户
async def delete_user_example(session, user_id):
    success = await user_service.delete_user(session, user_id)
    if success:
        print("用户删除成功")
    else:
        print("用户不存在或删除失败")
    return success
```

### 用户认证

```python
# 用户认证
async def authenticate_example(session):
    user = await user_service.authenticate_user(
        session,
        identifier="john_doe",  # 用户名或邮箱
        password="password123"
    )
    
    if user:
        print(f"认证成功: {user.username}")
    else:
        print("认证失败：用户名或密码错误")
    
    return user
```

## 🔐 认证服务使用

### 密码管理

```python
from services import auth_service, PasswordManager

# 使用认证服务的密码管理
password_manager = auth_service.password_manager

# 哈希密码
hashed = password_manager.hash_password("password123")
print(f"哈希后的密码: {hashed}")

# 验证密码
is_valid = password_manager.verify_password("password123", hashed)
print(f"密码验证结果: {is_valid}")

# 检查密码强度
strength = password_manager.check_password_strength("Password123!")
print(f"密码强度: {strength['strength']}")
print(f"建议: {strength['feedback']}")

# 生成随机密码
random_password = password_manager.generate_random_password(16)
print(f"随机密码: {random_password}")
```

### JWT令牌管理

```python
from services import auth_service

# 用户登录
async def login_example(session):
    from schemas.users import UserLogin
    
    login_data = UserLogin(
        identifier="john_doe",
        password="password123"
    )
    
    result = await auth_service.login(session, login_data)
    if result:
        print(f"登录成功，令牌: {result.access_token}")
        print(f"用户信息: {result.user.username}")
        return result
    else:
        print("登录失败")
        return None

# 验证令牌并获取当前用户
async def get_current_user_example(session, token):
    user = await auth_service.get_current_user(session, token)
    if user:
        print(f"当前用户: {user.username}")
    else:
        print("令牌无效或已过期")
    return user

# JWT令牌操作
jwt_manager = auth_service.jwt_manager

# 创建令牌
token = jwt_manager.create_access_token(
    subject="user123",
    additional_claims={"role": "admin"}
)

# 验证令牌
payload = jwt_manager.verify_token(token)
if payload:
    print(f"令牌有效，用户ID: {payload['sub']}")
else:
    print("令牌无效")

# 检查令牌是否过期
is_expired = jwt_manager.is_token_expired(token)
print(f"令牌是否过期: {is_expired}")
```

## 🔄 事务管理

### 基本事务

```python
from sqlalchemy.ext.asyncio import AsyncSession

async def transaction_example(session: AsyncSession):
    try:
        # 开始事务
        async with session.begin():
            # 创建用户
            user_data = UserCreate(...)
            user = await user_service.create_user(session, user_data, commit=False)
            
            # 其他相关操作
            # ...
            
            # 事务会自动提交
            
    except Exception as e:
        # 事务会自动回滚
        print(f"事务失败: {e}")
        raise
```

### 手动事务控制

```python
async def manual_transaction_example(session: AsyncSession):
    try:
        # 创建用户（不自动提交）
        user = await user_service.create_user(session, user_data, commit=False)
        
        # 其他操作
        # ...
        
        # 手动提交
        await session.commit()
        
    except Exception as e:
        # 手动回滚
        await session.rollback()
        raise
```

## 🧪 测试示例

```python
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from services import user_service, ConflictError

@pytest.mark.asyncio
async def test_create_user(session: AsyncSession):
    """测试用户创建"""
    user_data = UserCreate(
        username="test_user",
        email="<EMAIL>",
        password="password123",
        confirm_password="password123"
    )
    
    user = await user_service.create_user(session, user_data)
    
    assert user.username == "test_user"
    assert user.email == "<EMAIL>"
    assert user.id is not None

@pytest.mark.asyncio
async def test_duplicate_username(session: AsyncSession):
    """测试重复用户名"""
    user_data = UserCreate(
        username="duplicate_user",
        email="<EMAIL>",
        password="password123",
        confirm_password="password123"
    )
    
    # 第一次创建应该成功
    await user_service.create_user(session, user_data)
    
    # 第二次创建应该失败
    user_data.email = "<EMAIL>"
    with pytest.raises(ConflictError):
        await user_service.create_user(session, user_data)
```

## 📝 最佳实践

1. **异常处理**: 始终捕获和处理服务层异常
2. **事务管理**: 在需要原子性的操作中使用事务
3. **数据验证**: 依赖Pydantic Schema进行数据验证
4. **密码安全**: 使用认证服务的密码管理功能
5. **令牌管理**: 正确处理JWT令牌的生成和验证
6. **日志记录**: 服务层会自动记录重要操作的日志
7. **类型安全**: 利用完整的类型注解进行开发

## 🔗 相关文档

- [数据库模型文档](../models/README.md)
- [API Schema文档](../schemas/README.md)
- [数据库迁移指南](../migrations/README.md)
