#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 17:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : users.py
# @Update  : 2025/8/7 17:00 优化的用户服务实现

"""
用户服务层模块 - 优化版本

该模块实现用户相关的业务逻辑和数据库操作，遵循Context7最佳实践：
- 现代化的异步CRUD操作
- 完整的用户认证和密码管理
- 高性能的查询和分页
- 统一的异常处理
- 类型安全的接口设计

主要功能：
- 用户的CRUD操作（创建、查询、更新、删除）
- 用户认证和密码管理
- 用户查询和分页
- 业务逻辑验证和处理
- 数据完整性保护

参考：SQLAlchemy CRUD Plus 最佳实践
"""

from __future__ import annotations

import uuid
from typing import Any, Dict, Optional, Sequence, Union

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from models.users import Users
from schemas.users import (UserCreate, UserListResponse, UserQuery, UserResponse, UserUpdate)
from services.base import BaseService, ConflictError, NotFoundError, ValidationError


class UserService(BaseService[Users, UserCreate, UserUpdate]):
    """
    用户服务类 - 优化版本
    
    提供用户相关的所有业务逻辑操作，继承自BaseService
    获得通用的CRUD功能，并添加用户特定的业务逻辑。
    
    主要特性：
    - 完整的用户CRUD操作
    - 用户名和邮箱唯一性验证
    - 密码安全管理
    - 高性能查询和分页
    - 用户认证支持
    
    Type Parameters:
        Users: 用户模型类型
        UserCreate: 用户创建Schema类型
        UserUpdate: 用户更新Schema类型
    """

    def __init__(self):
        """初始化用户服务"""
        super().__init__(Users)
        # 延迟导入避免循环依赖
        self._password_manager = None

    @property
    def password_manager(self):
        """获取密码管理器实例（延迟加载）"""
        if self._password_manager is None:
            from services.auth import PasswordManager

            self._password_manager = PasswordManager()
        return self._password_manager

    async def create_user(
        self,
        session: AsyncSession,
        user_data: UserCreate,
        *,
        commit: bool = True
    ) -> Users:
        """
        创建新用户
        
        Args:
            session: 数据库会话
            user_data: 用户创建数据
            commit: 是否自动提交事务
            
        Returns:
            Users: 创建的用户对象
            
        Raises:
            ConflictError: 用户名或邮箱已存在时抛出
            ValidationError: 数据验证失败时抛出
        """
        # 创建用户数据字典，处理密码哈希
        user_dict = user_data.model_dump(exclude={'password', 'confirm_password'})
        user_dict['password'] = self.password_manager.hash_password(user_data.password)

        # 使用基类的创建方法
        return await self.create(session, user_dict, commit=commit)

    async def get_user_by_username(
        self,
        session: AsyncSession,
        username: str
    ) -> Optional[Users]:
        """
        根据用户名获取用户
        
        Args:
            session: 数据库会话
            username: 用户名
            
        Returns:
            Optional[Users]: 用户对象，如果不存在则返回None
        """
        return await self._get_user_by_field(session, "username", username)

    async def get_user_by_email(
        self,
        session: AsyncSession,
        email: str
    ) -> Optional[Users]:
        """
        根据邮箱获取用户
        
        Args:
            session: 数据库会话
            email: 邮箱地址
            
        Returns:
            Optional[Users]: 用户对象，如果不存在则返回None
        """
        return await self._get_user_by_field(session, "email", email)

    async def get_users(
        self,
        session: AsyncSession,
        query: UserQuery
    ) -> UserListResponse:
        """
        获取用户列表（支持分页和筛选）
        
        Args:
            session: 数据库会话
            query: 查询参数
            
        Returns:
            UserListResponse: 包含用户列表和分页信息的响应
        """
        # 构建过滤条件
        filters = {}
        if query.username:
            filters["username__like"] = f"%{query.username}%"
        if query.email:
            filters["email__like"] = f"%{query.email}%"

        # 计算偏移量
        offset = (query.page - 1) * query.size

        # 获取用户列表
        users = await self.get_multi(
            session,
            skip=offset,
            limit=query.size,
            filters=filters,
            order_by=query.sort_by,
            order_desc=(query.sort_order == "desc")
        )

        # 获取总数
        total = await self.count(session, filters)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return UserListResponse(
            users=[UserResponse.model_validate(user) for user in users],
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def update_user(
        self,
        session: AsyncSession,
        user_id: uuid.UUID,
        user_data: UserUpdate,
        *,
        commit: bool = True
    ) -> Optional[Users]:
        """
        更新用户信息
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            user_data: 更新数据
            commit: 是否自动提交事务
            
        Returns:
            Optional[Users]: 更新后的用户对象，如果用户不存在则返回None
            
        Raises:
            ConflictError: 用户名或邮箱已被其他用户使用时抛出
        """
        # 处理密码更新
        update_dict = user_data.model_dump(exclude_unset=True)
        if "password" in update_dict:
            update_dict["password"] = self.password_manager.hash_password(update_dict["password"])

        # 使用基类的更新方法
        return await self.update(session, user_id, update_dict, commit=commit)

    async def delete_user(
        self,
        session: AsyncSession,
        user_id: uuid.UUID,
        *,
        commit: bool = True
    ) -> bool:
        """
        删除用户
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            commit: 是否自动提交事务
            
        Returns:
            bool: 是否成功删除
        """
        return await self.delete(session, user_id, commit=commit)

    async def authenticate_user(
        self,
        session: AsyncSession,
        identifier: str,
        password: str
    ) -> Optional[Users]:
        """
        用户认证
        
        Args:
            session: 数据库会话
            identifier: 登录标识符（用户名或邮箱）
            password: 密码
            
        Returns:
            Optional[Users]: 认证成功返回用户对象，失败返回None
        """
        # 尝试通过用户名查找
        user = await self.get_user_by_username(session, identifier)

        # 如果用户名查找失败，尝试通过邮箱查找
        if not user:
            user = await self.get_user_by_email(session, identifier)

        # 验证密码
        if user and self.password_manager.verify_password(password, user.password):
            return user

        return None

    async def change_password(
        self,
        session: AsyncSession,
        user_id: uuid.UUID,
        old_password: str,
        new_password: str,
        *,
        commit: bool = True
    ) -> bool:
        """
        修改用户密码
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            old_password: 旧密码
            new_password: 新密码
            commit: 是否自动提交事务
            
        Returns:
            bool: 是否修改成功
            
        Raises:
            NotFoundError: 用户不存在
            ValidationError: 旧密码错误或新密码不符合要求
        """
        # 获取用户
        user = await self.get_by_id(session, user_id)
        if not user:
            raise NotFoundError("User", user_id)

        # 验证旧密码
        if not self.password_manager.verify_password(old_password, user.password):
            raise ValidationError("旧密码错误")

        # 检查新密码强度
        strength = self.password_manager.check_password_strength(new_password)
        if not strength["is_strong"]:
            raise ValidationError(f"新密码强度不足: {', '.join(strength['feedback'])}")

        # 更新密码
        user.password = self.password_manager.hash_password(new_password)

        if commit:
            await session.commit()

        return True

    async def reset_password(
        self,
        session: AsyncSession,
        identifier: str,
        new_password: str,
        *,
        commit: bool = True
    ) -> bool:
        """
        重置用户密码（管理员功能）

        Args:
            session: 数据库会话
            identifier: 用户标识符（用户名或邮箱）
            new_password: 新密码
            commit: 是否自动提交事务

        Returns:
            bool: 是否重置成功

        Raises:
            NotFoundError: 用户不存在
            ValidationError: 新密码不符合要求
        """
        # 查找用户
        user = await self.get_user_by_username(session, identifier)
        if not user:
            user = await self.get_user_by_email(session, identifier)

        if not user:
            raise NotFoundError("User", identifier)

        # 检查新密码强度
        strength = self.password_manager.check_password_strength(new_password)
        if not strength["is_strong"]:
            raise ValidationError(f"新密码强度不足: {', '.join(strength['feedback'])}")

        # 更新密码
        user.password = self.password_manager.hash_password(new_password)

        if commit:
            await session.commit()

        return True

    async def get_user_statistics(
        self,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """
        获取用户统计信息

        Args:
            session: 数据库会话

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            # 总用户数
            total_users = await self.count(session)

            # 今日新增用户数
            from datetime import date

            today = date.today()
            today_filters = {"created_at__ge": today}
            today_new_users = await self.count(session, today_filters)

            # 本月新增用户数
            from datetime import datetime

            month_start = datetime(today.year, today.month, 1).date()
            month_filters = {"created_at__ge": month_start}
            month_new_users = await self.count(session, month_filters)

            return {
                "total_users":     total_users,
                "today_new_users": today_new_users,
                "month_new_users": month_new_users,
                "statistics_date": today.isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error getting user statistics: {str(e)}")
            raise

    async def search_users(
        self,
        session: AsyncSession,
        search_term: str,
        *,
        limit: int = 20
    ) -> Sequence[Users]:
        """
        搜索用户

        Args:
            session: 数据库会话
            search_term: 搜索词
            limit: 限制返回数量

        Returns:
            Sequence[Users]: 匹配的用户列表
        """
        if not search_term.strip():
            return []

        # 构建搜索过滤条件
        search_filters = {
            "__or__": {
                "username__ilike": f"%{search_term}%",
                "email__ilike":    f"%{search_term}%",
                "comment__ilike":  f"%{search_term}%"
            }
        }

        return await self.get_multi(
            session,
            filters=search_filters,
            limit=limit,
            order_by="username"
        )

    # 私有辅助方法

    async def _get_user_by_field(
        self,
        session: AsyncSession,
        field: str,
        value: str
    ) -> Optional[Users]:
        """根据指定字段获取用户（内部方法）"""
        try:
            stmt = select(Users).where(getattr(Users, field) == value)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"Error getting user by {field} {value}: {str(e)}")
            return None

    # 实现基类的抽象方法

    async def validate_create(
        self,
        session: AsyncSession,
        obj_in: Union[UserCreate, Dict[str, Any]]
    ) -> None:
        """
        创建前的数据验证

        Args:
            session: 数据库会话
            obj_in: 创建数据Schema

        Raises:
            ConflictError: 用户名或邮箱已存在
            ValidationError: 数据验证失败
        """
        # 获取数据
        if hasattr(obj_in, 'username'):
            username = obj_in.username
            email = obj_in.email
        else:
            username = obj_in.get('username')
            email = obj_in.get('email')

        # 检查用户名是否已存在
        if username:
            existing_user = await self._get_user_by_field(session, "username", username)
            if existing_user:
                raise ConflictError("用户名已存在", "username", username)

        # 检查邮箱是否已存在
        if email:
            existing_email = await self._get_user_by_field(session, "email", email)
            if existing_email:
                raise ConflictError("邮箱已存在", "email", email)

    async def validate_update(
        self,
        session: AsyncSession,
        db_obj: Users,
        obj_in: Union[UserUpdate, Dict[str, Any]]
    ) -> None:
        """
        更新前的数据验证

        Args:
            session: 数据库会话
            db_obj: 现有的数据库对象
            obj_in: 更新数据Schema

        Raises:
            ConflictError: 用户名或邮箱已被其他用户使用
        """
        # 获取数据
        if hasattr(obj_in, 'username'):
            username = obj_in.username
            email = obj_in.email
        else:
            username = obj_in.get('username')
            email = obj_in.get('email')

        # 检查用户名唯一性（如果要更新用户名）
        if username and username != db_obj.username:
            existing_user = await self._get_user_by_field(session, "username", username)
            if existing_user and existing_user.id != db_obj.id:
                raise ConflictError("用户名已存在", "username", username)

        # 检查邮箱唯一性（如果要更新邮箱）
        if email and email != db_obj.email:
            existing_email = await self._get_user_by_field(session, "email", email)
            if existing_email and existing_email.id != db_obj.id:
                raise ConflictError("邮箱已存在", "email", email)


# 创建用户服务实例
user_service = UserService()
