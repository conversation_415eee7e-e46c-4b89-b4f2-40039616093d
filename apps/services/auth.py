#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 17:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : auth.py
# @Update  : 2025/8/7 17:00 优化的认证服务实现

"""
认证服务模块 - 优化版本

该模块提供用户认证相关的功能，遵循Context7和FastAPI JWT最佳实践：
- 现代化的密码哈希和验证（bcrypt）
- 安全的JWT令牌生成和验证
- 完整的用户认证流程
- 密码强度验证和安全策略
- 令牌刷新和撤销机制

安全特性：
- bcrypt密码哈希（12轮）
- JWT令牌管理（HS256算法）
- 密码强度验证
- 防止常见安全攻击
- 安全的会话处理

参考：FastAPI JWT 最佳实践
"""

from __future__ import annotations

import re
import secrets
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, Union

import bcrypt
import jwt
from sqlalchemy.ext.asyncio import AsyncSession

from configs import settings
from models.users import Users
from schemas.users import UserLogin, UserLoginResponse, UserResponse
from services.base import ServiceError


class PasswordManager:
    """
    密码管理器 - 优化版本
    
    提供安全的密码哈希和验证功能，使用bcrypt算法
    确保密码的安全存储和验证。
    
    安全特性：
    - bcrypt哈希算法（可配置轮数）
    - 自动盐值生成
    - 密码强度验证
    - 安全的密码比较
    - 随机密码生成
    
    Example:
        >>> password_manager = PasswordManager()
        >>> hashed = password_manager.hash_password("password123")
        >>> is_valid = password_manager.verify_password("password123", hashed)
    """

    def __init__(self, rounds: int = 12):
        """
        初始化密码管理器
        
        Args:
            rounds: bcrypt轮数，默认12轮（推荐值，安全性与性能的平衡）
        """
        self.rounds = rounds
        self.min_length = 8
        self.max_length = 128

    def hash_password(self, password: str) -> str:
        """
        哈希密码
        
        Args:
            password: 明文密码
            
        Returns:
            str: 哈希后的密码
            
        Raises:
            ValueError: 密码格式不正确
        """
        if not password:
            raise ValueError("密码不能为空")

        if len(password) < self.min_length:
            raise ValueError(f"密码长度至少{self.min_length}个字符")

        if len(password) > self.max_length:
            raise ValueError(f"密码长度不能超过{self.max_length}个字符")

        # 将密码编码为字节
        password_bytes = password.encode('utf-8')

        # 生成盐值并哈希
        salt = bcrypt.gensalt(rounds=self.rounds)
        hashed = bcrypt.hashpw(password_bytes, salt)

        # 返回字符串格式
        return hashed.decode('utf-8')

    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """
        验证密码
        
        Args:
            password: 明文密码
            hashed_password: 哈希后的密码
            
        Returns:
            bool: 密码是否正确
        """
        try:
            if not password or not hashed_password:
                return False

            password_bytes = password.encode('utf-8')
            hashed_bytes = hashed_password.encode('utf-8')
            return bcrypt.checkpw(password_bytes, hashed_bytes)
        except (ValueError, TypeError, UnicodeError):
            return False

    def generate_random_password(self, length: int = 16) -> str:
        """
        生成随机密码
        
        Args:
            length: 密码长度，默认16位
            
        Returns:
            str: 随机生成的密码
        """
        if length < self.min_length:
            length = self.min_length
        if length > self.max_length:
            length = self.max_length

        # 定义字符集，确保包含各种类型的字符
        lowercase = "abcdefghijklmnopqrstuvwxyz"
        uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        digits = "0123456789"
        special = "!@#$%^&*()_+-=[]{}|;:,.<>?"

        # 确保至少包含每种类型的字符
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(special)
        ]

        # 填充剩余长度
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))

        # 随机打乱顺序
        secrets.SystemRandom().shuffle(password)

        return ''.join(password)

    def check_password_strength(self, password: str) -> Dict[str, Union[bool, int, str, list]]:
        """
        检查密码强度
        
        Args:
            password: 要检查的密码
            
        Returns:
            dict: 包含密码强度信息的字典
        """
        score = 0
        feedback = []

        # 长度检查
        if len(password) >= self.min_length:
            score += 1
        else:
            feedback.append(f"密码长度至少{self.min_length}个字符")

        if len(password) >= 12:
            score += 1

        if len(password) >= 16:
            score += 1

        # 字符类型检查
        has_lower = bool(re.search(r'[a-z]', password))
        has_upper = bool(re.search(r'[A-Z]', password))
        has_digit = bool(re.search(r'\d', password))
        has_special = bool(re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password))

        if has_lower:
            score += 1
        else:
            feedback.append("需要包含小写字母")

        if has_upper:
            score += 1
        else:
            feedback.append("需要包含大写字母")

        if has_digit:
            score += 1
        else:
            feedback.append("需要包含数字")

        if has_special:
            score += 1
        else:
            feedback.append("需要包含特殊字符")

        # 复杂性检查
        if len(set(password)) >= len(password) * 0.7:  # 字符多样性
            score += 1
        else:
            feedback.append("密码字符重复过多")

        # 常见密码检查
        common_patterns = [
            r'123456', r'password', r'qwerty', r'abc123',
            r'admin', r'letmein', r'welcome', r'monkey'
        ]

        for pattern in common_patterns:
            if re.search(pattern, password.lower()):
                score -= 2
                feedback.append("避免使用常见密码模式")
                break

        # 计算强度等级
        max_score = 8
        score = max(0, min(score, max_score))

        if score >= 7:
            strength = "很强"
        elif score >= 5:
            strength = "强"
        elif score >= 3:
            strength = "中等"
        else:
            strength = "弱"

        return {
            "score":      score,
            "max_score":  max_score,
            "strength":   strength,
            "is_strong":  score >= 5,
            "feedback":   feedback,
            "percentage": int((score / max_score) * 100)
        }


class JWTManager:
    """
    JWT令牌管理器 - 优化版本

    提供JWT令牌的生成、验证和管理功能，
    遵循FastAPI JWT最佳实践。

    安全特性：
    - JWT令牌生成和验证
    - 令牌过期时间管理
    - 安全的密钥管理
    - 令牌刷新机制
    - 防止令牌重放攻击

    Example:
        >>> jwt_manager = JWTManager()
        >>> token = jwt_manager.create_access_token(user_id="123")
        >>> payload = jwt_manager.verify_token(token)
    """

    def __init__(
        self,
        secret_key: Optional[str] = None,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 30,
        refresh_token_expire_days: int = 7
    ):
        """
        初始化JWT管理器

        Args:
            secret_key: JWT密钥，如果为None则使用配置中的密钥
            algorithm: 签名算法，默认HS256
            access_token_expire_minutes: 访问令牌过期时间（分钟）
            refresh_token_expire_days: 刷新令牌过期时间（天）
        """
        self.secret_key = secret_key or settings.jwt.secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days

        # 验证密钥强度
        if len(self.secret_key) < 32:
            raise ValueError("JWT密钥长度至少32个字符")

    def create_access_token(
        self,
        subject: Union[str, int],
        expires_delta: Optional[timedelta] = None,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        创建访问令牌

        Args:
            subject: 令牌主体（通常是用户ID）
            expires_delta: 自定义过期时间
            additional_claims: 额外的声明

        Returns:
            str: JWT令牌
        """
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                minutes=self.access_token_expire_minutes
                )

        # 构建载荷
        payload = {
            "sub":  str(subject),
            "exp":  expire,
            "iat":  datetime.now(timezone.utc),
            "type": "access",
            "jti":  secrets.token_urlsafe(16)  # JWT ID，防止重放攻击
        }

        # 添加额外声明
        if additional_claims:
            # 过滤保留字段
            reserved_claims = {"sub", "exp", "iat", "type", "jti"}
            filtered_claims = {k: v for k, v in additional_claims.items() if
                               k not in reserved_claims}
            payload.update(filtered_claims)

        # 生成令牌
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        return token

    def create_refresh_token(
        self,
        subject: Union[str, int],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        创建刷新令牌

        Args:
            subject: 令牌主体（通常是用户ID）
            expires_delta: 自定义过期时间

        Returns:
            str: JWT刷新令牌
        """
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(days=self.refresh_token_expire_days)

        # 构建载荷
        payload = {
            "sub":  str(subject),
            "exp":  expire,
            "iat":  datetime.now(timezone.utc),
            "type": "refresh",
            "jti":  secrets.token_urlsafe(16)
        }

        # 生成令牌
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        return token

    def verify_token(self, token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
        """
        验证令牌

        Args:
            token: JWT令牌
            token_type: 令牌类型（access或refresh）

        Returns:
            Optional[dict]: 令牌载荷，如果无效则返回None
        """
        try:
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm]
            )

            # 验证令牌类型
            if payload.get("type") != token_type:
                return None

            return payload

        except jwt.ExpiredSignatureError:
            # 令牌已过期
            return None
        except jwt.InvalidTokenError:
            # 令牌无效
            return None
        except Exception:
            # 其他错误
            return None

    def get_token_subject(self, token: str) -> Optional[str]:
        """
        获取令牌主体

        Args:
            token: JWT令牌

        Returns:
            Optional[str]: 令牌主体，如果无效则返回None
        """
        payload = self.verify_token(token)
        return payload.get("sub") if payload else None

    def get_token_jti(self, token: str) -> Optional[str]:
        """
        获取令牌JTI（JWT ID）

        Args:
            token: JWT令牌

        Returns:
            Optional[str]: 令牌JTI，如果无效则返回None
        """
        payload = self.verify_token(token)
        return payload.get("jti") if payload else None

    def is_token_expired(self, token: str) -> bool:
        """
        检查令牌是否过期

        Args:
            token: JWT令牌

        Returns:
            bool: 是否过期
        """
        try:
            jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            return False
        except jwt.ExpiredSignatureError:
            return True
        except jwt.InvalidTokenError:
            return True

    def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """
        使用刷新令牌生成新的访问令牌

        Args:
            refresh_token: 刷新令牌

        Returns:
            Optional[str]: 新的访问令牌，如果刷新令牌无效则返回None
        """
        payload = self.verify_token(refresh_token, token_type="refresh")
        if not payload:
            return None

        # 生成新的访问令牌
        return self.create_access_token(subject=payload["sub"])


class AuthService:
    """
    认证服务 - 优化版本

    提供完整的用户认证功能，集成密码管理和JWT令牌管理。

    主要功能：
    - 用户登录认证
    - 密码安全管理
    - JWT令牌生成和验证
    - 用户会话管理
    - 令牌刷新机制
    """

    def __init__(self):
        """初始化认证服务"""
        self.password_manager = PasswordManager()
        self.jwt_manager = JWTManager()

    async def login(
        self,
        session: AsyncSession,
        login_data: UserLogin
    ) -> Optional[UserLoginResponse]:
        """
        用户登录

        Args:
            session: 数据库会话
            login_data: 登录数据

        Returns:
            Optional[UserLoginResponse]: 登录成功返回响应数据，失败返回None

        Raises:
            ServiceError: 登录过程中发生错误
        """
        try:
            # 延迟导入避免循环依赖
            from services.users import user_service

            # 认证用户
            user = await user_service.authenticate_user(
                session,
                login_data.identifier,
                login_data.password
            )

            if not user:
                return None

            # 生成访问令牌和刷新令牌
            access_token = self.jwt_manager.create_access_token(
                subject=str(user.id),
                additional_claims={
                    "username": user.username,
                    "email":    user.email
                }
            )

            refresh_token = self.jwt_manager.create_refresh_token(
                subject=str(user.id)
            )

            # 构建响应
            return UserLoginResponse(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_in=self.jwt_manager.access_token_expire_minutes * 60,
                user=UserResponse.model_validate(user)
            )

        except Exception as e:
            raise ServiceError(f"登录失败: {str(e)}", original_error=e)

    async def refresh_token(
        self,
        session: AsyncSession,
        refresh_token: str
    ) -> Optional[Dict[str, str]]:
        """
        刷新访问令牌

        Args:
            session: 数据库会话
            refresh_token: 刷新令牌

        Returns:
            Optional[Dict[str, str]]: 新的令牌对，如果失败则返回None
        """
        try:
            # 验证刷新令牌
            payload = self.jwt_manager.verify_token(refresh_token, token_type="refresh")
            if not payload:
                return None

            # 验证用户是否仍然存在
            from services.users import user_service

            user = await user_service.get_user_by_id(session, payload["sub"])
            if not user:
                return None

            # 生成新的令牌对
            new_access_token = self.jwt_manager.create_access_token(
                subject=str(user.id),
                additional_claims={
                    "username": user.username,
                    "email":    user.email
                }
            )

            new_refresh_token = self.jwt_manager.create_refresh_token(
                subject=str(user.id)
            )

            return {
                "access_token":  new_access_token,
                "refresh_token": new_refresh_token,
                "token_type":    "bearer",
                "expires_in":    self.jwt_manager.access_token_expire_minutes * 60
            }

        except Exception as e:
            raise ServiceError(f"令牌刷新失败: {str(e)}", original_error=e)

    async def get_current_user(
        self,
        session: AsyncSession,
        token: str
    ) -> Optional[Users]:
        """
        根据令牌获取当前用户

        Args:
            session: 数据库会话
            token: JWT令牌

        Returns:
            Optional[Users]: 用户对象，如果令牌无效则返回None
        """
        try:
            # 验证令牌
            payload = self.jwt_manager.verify_token(token)
            if not payload:
                return None

            # 获取用户ID
            user_id = payload.get("sub")
            if not user_id:
                return None

            # 查询用户
            from services.users import user_service

            user = await user_service.get_user_by_id(session, user_id)
            return user

        except Exception:
            return None

    def hash_password(self, password: str) -> str:
        """哈希密码"""
        return self.password_manager.hash_password(password)

    def verify_password(self, password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.password_manager.verify_password(password, hashed_password)

    def check_password_strength(self, password: str) -> Dict[str, Any]:
        """检查密码强度"""
        return self.password_manager.check_password_strength(password)

    def generate_random_password(self, length: int = 16) -> str:
        """生成随机密码"""
        return self.password_manager.generate_random_password(length)


# 创建认证服务实例
auth_service = AuthService()
