#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 17:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : base.py
# @Update  : 2025/8/7 17:00 优化的基础服务类实现

"""
基础服务模块 - 优化版本

该模块定义了服务层的基础类和通用功能，遵循Context7最佳实践：
- 现代化的异步CRUD操作模式
- 统一的错误处理和异常管理
- 类型安全的泛型设计
- 高性能的数据库操作
- 完整的事务管理支持

设计原则：
- 单一职责：每个服务类负责特定领域的业务逻辑
- 依赖注入：通过构造函数注入依赖项
- 异常处理：统一的异常处理和错误信息
- 类型安全：完整的类型注解支持
- 可测试性：便于单元测试和集成测试

参考：SQLAlchemy CRUD Plus 最佳实践
"""

from __future__ import annotations

import logging
import uuid
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, Sequence, Type, TypeVar, Union

from sqlalchemy import and_, func, or_, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from models.base_model import BaseModel

# 类型变量定义
ModelType = TypeVar("ModelType", bound=BaseModel)
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")

# 配置日志
logger = logging.getLogger(__name__)


class ServiceError(Exception):
    """服务层基础异常类"""

    def __init__(
        self,
        message: str,
        code: str = "SERVICE_ERROR",
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        self.message = message
        self.code = code
        self.details = details or {}
        self.original_error = original_error
        super().__init__(self.message)


class NotFoundError(ServiceError):
    """资源未找到异常"""

    def __init__(self, resource: str, identifier: Any):
        message = f"{resource} with identifier '{identifier}' not found"
        super().__init__(
            message,
            "NOT_FOUND",
            {"resource": resource, "identifier": str(identifier)}
        )


class ConflictError(ServiceError):
    """资源冲突异常"""

    def __init__(self, message: str, field: Optional[str] = None, value: Optional[Any] = None):
        details = {}
        if field:
            details["field"] = field
        if value:
            details["value"] = str(value)
        super().__init__(message, "CONFLICT", details)


class ValidationError(ServiceError):
    """数据验证异常"""

    def __init__(self, message: str, field: Optional[str] = None, value: Optional[Any] = None):
        details = {}
        if field:
            details["field"] = field
        if value:
            details["value"] = str(value)
        super().__init__(message, "VALIDATION_ERROR", details)


class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType], ABC):
    """
    基础服务类 - 优化版本
    
    提供通用的CRUD操作和服务层功能，遵循SQLAlchemy CRUD Plus最佳实践。
    使用泛型来确保类型安全，支持不同的模型和Schema类型。
    
    主要特性：
    - 异步数据库操作
    - 统一的异常处理
    - 高性能查询优化
    - 完整的事务支持
    - 类型安全的接口
    
    Type Parameters:
        ModelType: SQLAlchemy模型类型
        CreateSchemaType: 创建操作的Pydantic Schema类型
        UpdateSchemaType: 更新操作的Pydantic Schema类型
    """

    def __init__(self, model: Type[ModelType]):
        """
        初始化基础服务
        
        Args:
            model: SQLAlchemy模型类
        """
        self.model = model
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    async def create(
        self,
        session: AsyncSession,
        obj_in: Union[CreateSchemaType, Dict[str, Any]],
        *,
        commit: bool = True,
        flush: bool = False
    ) -> ModelType:
        """
        创建新记录
        
        Args:
            session: 数据库会话
            obj_in: 创建数据Schema或字典
            commit: 是否自动提交事务
            flush: 是否刷新到数据库但不提交
            
        Returns:
            ModelType: 创建的模型实例
            
        Raises:
            ValidationError: 数据验证失败
            ConflictError: 数据冲突（如唯一约束违反）
        """
        try:
            # 执行创建前验证
            await self.validate_create(session, obj_in)

            # 将Pydantic模型转换为字典
            if hasattr(obj_in, 'model_dump'):
                obj_data = obj_in.model_dump()
            elif hasattr(obj_in, 'dict'):
                obj_data = obj_in.dict()
            else:
                obj_data = dict(obj_in)

            # 创建模型实例
            db_obj = self.model(**obj_data)
            session.add(db_obj)

            if flush:
                await session.flush()
                await session.refresh(db_obj)
            elif commit:
                await session.commit()
                await session.refresh(db_obj)

            self.logger.info(f"Created {self.model.__name__} with id: {db_obj.id}")
            return db_obj

        except IntegrityError as e:
            if commit:
                await session.rollback()
            self.logger.error(f"Integrity error creating {self.model.__name__}: {str(e)}")
            raise ConflictError(f"Data conflict when creating {self.model.__name__}")
        except Exception as e:
            if commit:
                await session.rollback()
            self.logger.error(f"Error creating {self.model.__name__}: {str(e)}")
            raise ServiceError(
                f"Failed to create {self.model.__name__}: {str(e)}", original_error=e
                )

    async def create_many(
        self,
        session: AsyncSession,
        objs_in: List[Union[CreateSchemaType, Dict[str, Any]]],
        *,
        commit: bool = True,
        flush: bool = False
    ) -> List[ModelType]:
        """
        批量创建记录
        
        Args:
            session: 数据库会话
            objs_in: 创建数据列表
            commit: 是否自动提交事务
            flush: 是否刷新到数据库但不提交
            
        Returns:
            List[ModelType]: 创建的模型实例列表
        """
        try:
            db_objs = []

            for obj_in in objs_in:
                # 执行创建前验证
                await self.validate_create(session, obj_in)

                # 转换数据
                if hasattr(obj_in, 'model_dump'):
                    obj_data = obj_in.model_dump()
                elif hasattr(obj_in, 'dict'):
                    obj_data = obj_in.dict()
                else:
                    obj_data = dict(obj_in)

                db_obj = self.model(**obj_data)
                session.add(db_obj)
                db_objs.append(db_obj)

            if flush:
                await session.flush()
                for db_obj in db_objs:
                    await session.refresh(db_obj)
            elif commit:
                await session.commit()
                for db_obj in db_objs:
                    await session.refresh(db_obj)

            self.logger.info(f"Created {len(db_objs)} {self.model.__name__} records")
            return db_objs

        except Exception as e:
            if commit:
                await session.rollback()
            self.logger.error(f"Error creating multiple {self.model.__name__}: {str(e)}")
            raise ServiceError(
                f"Failed to create multiple {self.model.__name__}: {str(e)}", original_error=e
                )

    async def get_by_id(
        self,
        session: AsyncSession,
        id: Union[uuid.UUID, int, str],
        *,
        load_relationships: Optional[List[str]] = None
    ) -> Optional[ModelType]:
        """
        根据ID获取记录
        
        Args:
            session: 数据库会话
            id: 记录ID
            load_relationships: 要预加载的关系列表
            
        Returns:
            Optional[ModelType]: 模型实例，如果不存在则返回None
        """
        try:
            stmt = select(self.model).where(self.model.id == id)

            # 添加关系预加载
            if load_relationships:
                for relationship in load_relationships:
                    if hasattr(self.model, relationship):
                        stmt = stmt.options(selectinload(getattr(self.model, relationship)))

            result = await session.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(f"Error getting {self.model.__name__} by id {id}: {str(e)}")
            raise ServiceError(f"Failed to get {self.model.__name__}: {str(e)}", original_error=e)

    async def get_multi(
        self,
        session: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False,
        load_relationships: Optional[List[str]] = None
    ) -> Sequence[ModelType]:
        """
        获取多条记录
        
        Args:
            session: 数据库会话
            skip: 跳过的记录数
            limit: 限制返回的记录数
            filters: 过滤条件字典
            order_by: 排序字段
            order_desc: 是否降序排列
            load_relationships: 要预加载的关系列表
            
        Returns:
            Sequence[ModelType]: 模型实例列表
        """
        try:
            stmt = select(self.model)

            # 应用过滤条件
            if filters:
                filter_conditions = self._build_filters(filters)
                if filter_conditions is not None:
                    stmt = stmt.where(filter_conditions)

            # 应用排序
            if order_by and hasattr(self.model, order_by):
                order_column = getattr(self.model, order_by)
                if order_desc:
                    stmt = stmt.order_by(order_column.desc())
                else:
                    stmt = stmt.order_by(order_column.asc())

            # 添加关系预加载
            if load_relationships:
                for relationship in load_relationships:
                    if hasattr(self.model, relationship):
                        stmt = stmt.options(selectinload(getattr(self.model, relationship)))

            # 应用分页
            stmt = stmt.offset(skip).limit(limit)

            result = await session.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(f"Error getting multiple {self.model.__name__}: {str(e)}")
            raise ServiceError(
                f"Failed to get {self.model.__name__} list: {str(e)}", original_error=e
                )

    async def update(
        self,
        session: AsyncSession,
        id: Union[uuid.UUID, int, str],
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
        *,
        commit: bool = True
    ) -> Optional[ModelType]:
        """
        更新记录

        Args:
            session: 数据库会话
            id: 记录ID
            obj_in: 更新数据Schema或字典
            commit: 是否自动提交事务

        Returns:
            Optional[ModelType]: 更新后的模型实例，如果记录不存在则返回None

        Raises:
            NotFoundError: 记录不存在
            ValidationError: 数据验证失败
        """
        try:
            db_obj = await self.get_by_id(session, id)
            if not db_obj:
                raise NotFoundError(self.model.__name__, id)

            # 执行更新前验证
            await self.validate_update(session, db_obj, obj_in)

            # 获取更新数据
            if hasattr(obj_in, 'model_dump'):
                update_data = obj_in.model_dump(exclude_unset=True)
            elif hasattr(obj_in, 'dict'):
                update_data = obj_in.dict(exclude_unset=True)
            else:
                update_data = dict(obj_in)

            # 更新字段
            for field, value in update_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)

            if commit:
                await session.commit()
                await session.refresh(db_obj)

            self.logger.info(f"Updated {self.model.__name__} with id: {id}")
            return db_obj

        except NotFoundError:
            raise
        except Exception as e:
            if commit:
                await session.rollback()
            self.logger.error(f"Error updating {self.model.__name__} with id {id}: {str(e)}")
            raise ServiceError(
                f"Failed to update {self.model.__name__}: {str(e)}", original_error=e
                )

    async def delete(
        self,
        session: AsyncSession,
        id: Union[uuid.UUID, int, str],
        *,
        commit: bool = True
    ) -> bool:
        """
        删除记录

        Args:
            session: 数据库会话
            id: 记录ID
            commit: 是否自动提交事务

        Returns:
            bool: 是否成功删除
        """
        try:
            db_obj = await self.get_by_id(session, id)
            if not db_obj:
                return False

            await session.delete(db_obj)

            if commit:
                await session.commit()

            self.logger.info(f"Deleted {self.model.__name__} with id: {id}")
            return True

        except Exception as e:
            if commit:
                await session.rollback()
            self.logger.error(f"Error deleting {self.model.__name__} with id {id}: {str(e)}")
            raise ServiceError(
                f"Failed to delete {self.model.__name__}: {str(e)}", original_error=e
                )

    async def count(
        self,
        session: AsyncSession,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        统计记录数量

        Args:
            session: 数据库会话
            filters: 过滤条件字典

        Returns:
            int: 记录数量
        """
        try:
            stmt = select(func.count(self.model.id))

            if filters:
                filter_conditions = self._build_filters(filters)
                if filter_conditions is not None:
                    stmt = stmt.where(filter_conditions)

            result = await session.execute(stmt)
            return result.scalar()

        except Exception as e:
            self.logger.error(f"Error counting {self.model.__name__}: {str(e)}")
            raise ServiceError(f"Failed to count {self.model.__name__}: {str(e)}", original_error=e)

    async def exists(
        self,
        session: AsyncSession,
        id: Union[uuid.UUID, int, str]
    ) -> bool:
        """
        检查记录是否存在

        Args:
            session: 数据库会话
            id: 记录ID

        Returns:
            bool: 记录是否存在
        """
        try:
            stmt = select(func.count(self.model.id)).where(self.model.id == id)
            result = await session.execute(stmt)
            return result.scalar() > 0
        except Exception as e:
            self.logger.error(
                f"Error checking existence of {self.model.__name__} with id {id}: {str(e)}"
                )
            raise ServiceError(
                f"Failed to check {self.model.__name__} existence: {str(e)}", original_error=e
                )

    def _build_filters(self, filters: Dict[str, Any]) -> Optional[Any]:
        """
        构建查询过滤条件

        支持SQLAlchemy CRUD Plus风格的过滤器：
        - 比较操作符：field__gt, field__lt, field__ge, field__le, field__ne
        - 范围操作符：field__in, field__not_in, field__between
        - 字符串操作符：field__like, field__ilike, field__startswith, field__endswith
        - 空值检查：field__is, field__is_not
        - OR条件：__or__

        Args:
            filters: 过滤条件字典

        Returns:
            Optional[Any]: SQLAlchemy过滤条件
        """
        conditions = []
        or_conditions = []

        for field_expr, value in filters.items():
            if value is None:
                continue

            # 处理OR条件
            if field_expr == "__or__":
                if isinstance(value, dict):
                    or_sub_conditions = []
                    for or_field, or_value in value.items():
                        or_condition = self._build_single_filter(or_field, or_value)
                        if or_condition is not None:
                            or_sub_conditions.append(or_condition)
                    if or_sub_conditions:
                        or_conditions.append(or_(*or_sub_conditions))
                continue

            # 处理普通条件
            condition = self._build_single_filter(field_expr, value)
            if condition is not None:
                conditions.append(condition)

        # 合并所有条件
        all_conditions = conditions + or_conditions
        return and_(*all_conditions) if all_conditions else None

    def _build_single_filter(self, field_expr: str, value: Any) -> Optional[Any]:
        """构建单个过滤条件"""
        if "__" in field_expr:
            field_name, operator = field_expr.rsplit("__", 1)
        else:
            field_name, operator = field_expr, "eq"

        if not hasattr(self.model, field_name):
            return None

        column = getattr(self.model, field_name)

        # 比较操作符
        if operator == "gt":
            return column > value
        elif operator == "ge":
            return column >= value
        elif operator == "lt":
            return column < value
        elif operator == "le":
            return column <= value
        elif operator == "ne":
            return column != value
        elif operator == "eq":
            return column == value
        # 范围操作符
        elif operator == "in":
            return column.in_(value) if isinstance(value, (list, tuple)) else None
        elif operator == "not_in":
            return ~column.in_(value) if isinstance(value, (list, tuple)) else None
        elif operator == "between":
            if isinstance(value, (list, tuple)) and len(value) == 2:
                return column.between(value[0], value[1])
        # 字符串操作符
        elif operator == "like":
            return column.like(value)
        elif operator == "ilike":
            return column.ilike(value)
        elif operator == "startswith":
            return column.like(f"{value}%")
        elif operator == "endswith":
            return column.like(f"%{value}")
        elif operator == "contains":
            return column.like(f"%{value}%")
        # 空值检查
        elif operator == "is":
            return column.is_(value)
        elif operator == "is_not":
            return column.is_not(value)

        return None

    @abstractmethod
    async def validate_create(
        self,
        session: AsyncSession,
        obj_in: Union[CreateSchemaType, Dict[str, Any]]
    ) -> None:
        """
        创建前的数据验证（子类需要实现）

        Args:
            session: 数据库会话
            obj_in: 创建数据Schema

        Raises:
            ValidationError: 验证失败时抛出
        """
        pass

    @abstractmethod
    async def validate_update(
        self,
        session: AsyncSession,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> None:
        """
        更新前的数据验证（子类需要实现）

        Args:
            session: 数据库会话
            db_obj: 现有的数据库对象
            obj_in: 更新数据Schema

        Raises:
            ValidationError: 验证失败时抛出
        """
        pass
