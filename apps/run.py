#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 18:00
# <AUTHOR> la<PERSON>o
# @Email   : <EMAIL>
# @File    : run.py
# @Update  : 2025/8/7 18:00 应用启动脚本

"""
EchoNote应用启动脚本

该脚本提供了多种启动方式和配置选项，遵循Context7最佳实践：
- 开发环境启动
- 生产环境启动
- 测试环境启动
- 性能调优配置
- 监控和日志配置

启动方式：
- python run.py --env development  # 开发环境
- python run.py --env production   # 生产环境
- python run.py --env testing      # 测试环境

参考：FastAPI部署最佳实践
"""

from __future__ import annotations

import argparse
import logging
import multiprocessing
import os
import sys
from pathlib import Path

import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from configs import settings

logger = logging.getLogger(__name__)


def get_uvicorn_config(environment: str) -> dict:
    """
    根据环境获取uvicorn配置
    
    Args:
        environment: 环境名称
        
    Returns:
        dict: uvicorn配置字典
    """
    # 基础配置
    config = {
        "fast_app": "main:fast_app",
        "host": "0.0.0.0",
        "port": 8000,
        "log_level": "info",
        "access_log": True,
    }
    
    if environment == "development":
        # 开发环境配置
        config.update({
            "reload": True,
            "reload_dirs": [str(project_root)],
            "reload_includes": ["*.py"],
            "reload_excludes": ["*.pyc", "__pycache__"],
            "log_level": "debug",
            "use_colors": True,
        })
        
    elif environment == "production":
        # 生产环境配置
        workers = multiprocessing.cpu_count() * 2 + 1
        config.update({
            "workers": workers,
            "worker_class": "uvicorn.workers.UvicornWorker",
            "max_requests": 1000,
            "max_requests_jitter": 100,
            "preload_app": True,
            "keepalive": 2,
            "log_level": "warning",
            "access_log": False,  # 使用nginx等反向代理的访问日志
        })
        
    elif environment == "testing":
        # 测试环境配置
        config.update({
            "workers": 1,
            "log_level": "error",
            "access_log": False,
        })
        
    elif environment == "staging":
        # 预发布环境配置
        config.update({
            "workers": 2,
            "log_level": "info",
            "reload": False,
        })
    
    return config


def setup_logging(environment: str) -> None:
    """
    配置日志系统
    
    Args:
        environment: 环境名称
    """
    log_level = {
        "development": logging.DEBUG,
        "testing": logging.ERROR,
        "staging": logging.INFO,
        "production": logging.WARNING,
    }.get(environment, logging.INFO)
    
    # 创建logs目录
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # 配置日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 配置处理器
    handlers = [logging.StreamHandler(sys.stdout)]
    
    if environment in ["production", "staging"]:
        # 生产和预发布环境添加文件日志
        handlers.append(
            logging.FileHandler(
                logs_dir / f"app_{environment}.log",
                encoding="utf-8"
            )
        )
    
    # 配置根日志器
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=handlers,
        force=True
    )


def check_environment() -> None:
    """检查环境配置"""
    logger.info("🔍 检查环境配置...")
    
    # 检查必要的环境变量
    required_vars = [
        "DATABASE_URL",
        "JWT_SECRET_KEY",
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ 缺少必要的环境变量: {', '.join(missing_vars)}")
        logger.error("请检查.env文件或环境变量配置")
        sys.exit(1)
    
    logger.info("✅ 环境配置检查通过")


def run_development() -> None:
    """运行开发环境"""
    logger.info("🔧 启动开发环境...")
    
    config = get_uvicorn_config("development")
    
    logger.info(f"📚 API文档: http://localhost:{config['port']}/docs")
    logger.info(f"📖 ReDoc文档: http://localhost:{config['port']}/redoc")
    logger.info(f"🔍 健康检查: http://localhost:{config['port']}/api/v1/health")
    
    uvicorn.run(**config)


def run_production() -> None:
    """运行生产环境"""
    logger.info("🚀 启动生产环境...")
    
    # 生产环境使用gunicorn
    try:
        import gunicorn.app.wsgiapp as wsgi
        
        config = get_uvicorn_config("production")
        
        # gunicorn配置
        gunicorn_config = [
            "main:fast_app",
            "--worker-class", "uvicorn.workers.UvicornWorker",
            "--workers", str(config["workers"]),
            "--bind", f"{config['host']}:{config['port']}",
            "--max-requests", str(config["max_requests"]),
            "--max-requests-jitter", str(config["max_requests_jitter"]),
            "--preload",
            "--keepalive", str(config["keepalive"]),
            "--log-level", config["log_level"],
        ]
        
        if not config["access_log"]:
            gunicorn_config.append("--disable-redirect-access-to-syslog")
        
        logger.info(f"🌐 服务启动在: http://{config['host']}:{config['port']}")
        
        # 启动gunicorn
        sys.argv = ["gunicorn"] + gunicorn_config
        wsgi.run()
        
    except ImportError:
        logger.warning("⚠️ 未安装gunicorn，使用uvicorn启动生产环境")
        config = get_uvicorn_config("production")
        uvicorn.run(**config)


def run_testing() -> None:
    """运行测试环境"""
    logger.info("🧪 启动测试环境...")
    
    config = get_uvicorn_config("testing")
    uvicorn.run(**config)


def run_staging() -> None:
    """运行预发布环境"""
    logger.info("🎭 启动预发布环境...")
    
    config = get_uvicorn_config("staging")
    uvicorn.run(**config)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="EchoNote应用启动脚本")
    
    parser.add_argument(
        "--env",
        choices=["development", "production", "testing", "staging"],
        default="development",
        help="运行环境 (默认: development)"
    )
    
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="绑定主机 (默认: 0.0.0.0)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="绑定端口 (默认: 8000)"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        help="工作进程数 (仅生产环境)"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用自动重载 (仅开发环境)"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.env)
    
    logger.info("🎯 EchoNote应用启动")
    logger.info(f"📦 环境: {args.env}")
    logger.info(f"🐍 Python版本: {sys.version}")
    logger.info(f"📁 工作目录: {project_root}")
    
    # 检查环境
    check_environment()
    
    # 根据环境启动应用
    if args.env == "development":
        run_development()
    elif args.env == "production":
        run_production()
    elif args.env == "testing":
        run_testing()
    elif args.env == "staging":
        run_staging()


if __name__ == "__main__":
    main()
