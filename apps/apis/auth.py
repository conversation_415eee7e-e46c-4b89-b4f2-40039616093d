#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 18:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : auth.py
# @Update  : 2025/8/7 18:00 认证API路由模块

"""
认证API路由模块

该模块定义了用户认证相关的所有API端点，遵循Context7和FastAPI最佳实践：
- OAuth2密码流认证
- JWT令牌管理
- 令牌刷新机制
- 密码强度验证
- 安全的认证流程

API端点：
- POST /auth/login - 用户登录
- POST /auth/refresh - 刷新令牌
- POST /auth/logout - 用户登出
- POST /auth/check-password-strength - 检查密码强度
- GET /auth/me - 获取当前用户信息（通过令牌）

参考：FastAPI JWT最佳实践和OAuth2规范
"""

from __future__ import annotations

from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from apis.dependencies import get_current_active_user, get_db_session
from models.users import Users
from schemas.users import UserLogin, UserLoginResponse, UserResponse
from services import auth_service
from services.base import ServiceError

# 创建认证路由器
router = APIRouter(
    prefix="/auth",
    tags=["用户认证"],
    responses={
        401: {"description": "认证失败"},
        400: {"description": "请求无效"},
    },
)


@router.post(
    "/login",
    response_model=UserLoginResponse,
    summary="用户登录",
    description="使用用户名/邮箱和密码进行登录，返回访问令牌和刷新令牌",
    responses={
        200: {
            "description": "登录成功",
            "model": UserLoginResponse
        },
        401: {
            "description": "认证失败",
            "content": {
                "application/json": {
                    "example": {"detail": "用户名或密码错误"}
                }
            }
        }
    }
)
async def login(
    login_data: UserLogin,
    session: AsyncSession = Depends(get_db_session)
) -> UserLoginResponse:
    """
    用户登录
    
    使用用户名或邮箱和密码进行登录认证。
    
    **参数**：
    - **identifier**: 登录标识符，可以是用户名或邮箱地址
    - **password**: 用户密码
    
    **返回**：
    - **access_token**: JWT访问令牌，用于API认证
    - **refresh_token**: JWT刷新令牌，用于获取新的访问令牌
    - **token_type**: 令牌类型，固定为"bearer"
    - **expires_in**: 访问令牌过期时间（秒）
    - **user**: 用户基本信息
    
    **注意**：
    - 访问令牌有效期较短（默认30分钟）
    - 刷新令牌有效期较长（默认7天）
    - 请妥善保管令牌，避免泄露
    """
    try:
        result = await auth_service.login(session, login_data)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        return result
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {e.message}"
        )


@router.post(
    "/token",
    response_model=UserLoginResponse,
    summary="OAuth2令牌端点",
    description="OAuth2密码流令牌端点，兼容标准OAuth2客户端",
    responses={
        200: {
            "description": "令牌获取成功",
            "model": UserLoginResponse
        },
        401: {
            "description": "认证失败",
            "content": {
                "application/json": {
                    "example": {"detail": "用户名或密码错误"}
                }
            }
        }
    }
)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    session: AsyncSession = Depends(get_db_session)
) -> UserLoginResponse:
    """
    OAuth2令牌端点
    
    标准的OAuth2密码流令牌端点，兼容OAuth2客户端。
    
    **表单参数**：
    - **username**: 用户名或邮箱地址
    - **password**: 用户密码
    - **scope**: OAuth2作用域（可选）
    - **grant_type**: 授权类型，必须是"password"
    
    **返回**：
    - **access_token**: JWT访问令牌
    - **refresh_token**: JWT刷新令牌
    - **token_type**: 令牌类型
    - **expires_in**: 过期时间
    - **user**: 用户信息
    """
    try:
        # 将OAuth2表单数据转换为UserLogin格式
        login_data = UserLogin(
            identifier=form_data.username,
            password=form_data.password
        )
        
        result = await auth_service.login(session, login_data)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return result
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {e.message}"
        )


@router.post(
    "/refresh",
    response_model=Dict[str, Any],
    summary="刷新访问令牌",
    description="使用刷新令牌获取新的访问令牌",
    responses={
        200: {
            "description": "令牌刷新成功",
            "content": {
                "application/json": {
                    "example": {
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "token_type": "bearer",
                        "expires_in": 1800
                    }
                }
            }
        },
        401: {
            "description": "刷新令牌无效或已过期",
            "content": {
                "application/json": {
                    "example": {"detail": "刷新令牌无效"}
                }
            }
        }
    }
)
async def refresh_token(
    refresh_token: str,
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    刷新访问令牌
    
    使用有效的刷新令牌获取新的访问令牌和刷新令牌。
    
    **参数**：
    - **refresh_token**: 有效的刷新令牌
    
    **返回**：
    - **access_token**: 新的访问令牌
    - **refresh_token**: 新的刷新令牌
    - **token_type**: 令牌类型
    - **expires_in**: 访问令牌过期时间
    
    **注意**：
    - 刷新令牌只能使用一次
    - 每次刷新都会返回新的令牌对
    - 旧的令牌在刷新后立即失效
    """
    try:
        result = await auth_service.refresh_token(session, refresh_token)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="刷新令牌无效或已过期"
            )
        
        return result
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"令牌刷新失败: {e.message}"
        )


@router.post(
    "/logout",
    status_code=status.HTTP_200_OK,
    summary="用户登出",
    description="用户登出，使当前令牌失效",
    responses={
        200: {
            "description": "登出成功",
            "content": {
                "application/json": {
                    "example": {"message": "登出成功"}
                }
            }
        }
    }
)
async def logout(
    current_user: Users = Depends(get_current_active_user)
) -> Dict[str, str]:
    """
    用户登出
    
    使当前用户的令牌失效。
    
    **注意**：
    - 需要提供有效的访问令牌
    - 登出后令牌立即失效
    - 客户端应该删除本地存储的令牌
    """
    # 在实际应用中，这里可以将令牌加入黑名单
    # 或者在数据库中记录令牌失效状态
    # 目前简单返回成功消息
    
    return {"message": "登出成功"}


@router.post(
    "/check-password-strength",
    response_model=Dict[str, Any],
    summary="检查密码强度",
    description="检查密码的强度和安全性",
    responses={
        200: {
            "description": "密码强度检查结果",
            "content": {
                "application/json": {
                    "example": {
                        "score": 7,
                        "max_score": 8,
                        "strength": "很强",
                        "is_strong": True,
                        "feedback": [],
                        "percentage": 87
                    }
                }
            }
        }
    }
)
async def check_password_strength(
    password: str
) -> Dict[str, Any]:
    """
    检查密码强度
    
    分析密码的强度和安全性，提供改进建议。
    
    **参数**：
    - **password**: 要检查的密码
    
    **返回**：
    - **score**: 密码强度评分（0-8）
    - **max_score**: 最高分数
    - **strength**: 强度等级（弱/中等/强/很强）
    - **is_strong**: 是否为强密码
    - **feedback**: 改进建议列表
    - **percentage**: 强度百分比
    
    **评分标准**：
    - 长度（8+字符：1分，12+字符：2分，16+字符：3分）
    - 字符类型（小写字母、大写字母、数字、特殊字符各1分）
    - 字符多样性（1分）
    - 避免常见模式（-2分如果包含）
    """
    try:
        result = auth_service.check_password_strength(password)
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"密码强度检查失败: {str(e)}"
        )


@router.get(
    "/me",
    response_model=UserResponse,
    summary="获取当前认证用户信息",
    description="通过令牌获取当前认证用户的详细信息",
    responses={
        200: {
            "description": "当前用户信息",
            "model": UserResponse
        },
        401: {
            "description": "令牌无效或已过期",
            "content": {
                "application/json": {
                    "example": {"detail": "无效的认证凭据"}
                }
            }
        }
    }
)
async def get_current_user_info(
    current_user: Users = Depends(get_current_active_user)
) -> UserResponse:
    """
    获取当前认证用户信息
    
    通过访问令牌获取当前认证用户的详细信息。
    
    **返回**：
    - 用户ID
    - 用户名
    - 邮箱地址
    - 创建时间
    - 更新时间
    - 备注信息
    
    **注意**：
    - 需要在请求头中提供有效的访问令牌
    - 格式：`Authorization: Bearer <access_token>`
    """
    return UserResponse.model_validate(current_user)
