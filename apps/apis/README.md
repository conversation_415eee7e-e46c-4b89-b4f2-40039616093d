# EchoNote API 使用指南

本指南介绍如何使用EchoNote项目的用户相关API，遵循Context7和FastAPI最佳实践。

## 📋 API概览

### 🏗️ API架构

```
FastAPI Application
├── /api/v1/auth/*     # 认证相关API
├── /api/v1/users/*    # 用户管理API
├── /api/v1/health     # 健康检查
└── /api/v1/info       # API信息
```

### 🔐 认证方式

- **认证类型**: JWT Bearer <PERSON>ken
- **令牌类型**: Access Token + Refresh Token
- **访问令牌有效期**: 30分钟
- **刷新令牌有效期**: 7天

## 🚀 快速开始

### 1. 用户注册

```bash
curl -X POST "http://localhost:8000/api/v1/users/" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john_doe",
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "confirm_password": "SecurePass123!",
    "comment": "新用户"
  }'
```

**响应示例**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "username": "john_doe",
  "email": "<EMAIL>",
  "comment": "新用户",
  "created_at": "2024-01-01T12:00:00+00:00",
  "updated_at": "2024-01-01T12:00:00+00:00"
}
```

### 2. 用户登录

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "john_doe",
    "password": "SecurePass123!"
  }'
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "john_doe",
    "email": "<EMAIL>",
    "comment": "新用户",
    "created_at": "2024-01-01T12:00:00+00:00",
    "updated_at": "2024-01-01T12:00:00+00:00"
  }
}
```

### 3. 使用令牌访问受保护的API

```bash
curl -X GET "http://localhost:8000/api/v1/users/me" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 📚 API端点详解

### 🔐 认证API (`/api/v1/auth`)

#### POST `/auth/login` - 用户登录
- **描述**: 使用用户名/邮箱和密码登录
- **请求体**: `UserLogin`
- **响应**: `UserLoginResponse`

#### POST `/auth/token` - OAuth2令牌端点
- **描述**: 标准OAuth2密码流端点
- **请求体**: `OAuth2PasswordRequestForm`
- **响应**: `UserLoginResponse`

#### POST `/auth/refresh` - 刷新令牌
- **描述**: 使用刷新令牌获取新的访问令牌
- **参数**: `refresh_token` (string)
- **响应**: 新的令牌对

#### POST `/auth/logout` - 用户登出
- **描述**: 使当前令牌失效
- **认证**: 需要访问令牌
- **响应**: 成功消息

#### GET `/auth/me` - 获取当前用户
- **描述**: 通过令牌获取用户信息
- **认证**: 需要访问令牌
- **响应**: `UserResponse`

#### POST `/auth/check-password-strength` - 密码强度检查
- **描述**: 检查密码强度和安全性
- **参数**: `password` (string)
- **响应**: 密码强度分析结果

### 👤 用户API (`/api/v1/users`)

#### POST `/users/` - 创建用户
- **描述**: 注册新用户账户
- **请求体**: `UserCreate`
- **响应**: `UserResponse`

#### GET `/users/` - 获取用户列表
- **描述**: 分页获取用户列表，支持筛选和排序
- **认证**: 需要访问令牌
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页大小 (默认: 10, 最大: 100)
  - `username`: 用户名筛选
  - `email`: 邮箱筛选
  - `sort_by`: 排序字段 (默认: created_at)
  - `sort_order`: 排序方向 (asc/desc, 默认: desc)
- **响应**: `UserListResponse`

#### GET `/users/me` - 获取当前用户信息
- **描述**: 获取当前认证用户的详细信息
- **认证**: 需要访问令牌
- **响应**: `UserResponse`

#### GET `/users/{user_id}` - 获取指定用户
- **描述**: 根据用户ID获取用户信息
- **认证**: 需要访问令牌
- **路径参数**: `user_id` (UUID)
- **响应**: `UserResponse`

#### PUT `/users/{user_id}` - 更新用户信息
- **描述**: 更新用户信息，只能更新自己的信息或管理员权限
- **认证**: 需要访问令牌 + 所有权验证
- **路径参数**: `user_id` (UUID)
- **请求体**: `UserUpdate`
- **响应**: `UserResponse`

#### DELETE `/users/{user_id}` - 删除用户
- **描述**: 删除用户账户
- **认证**: 需要管理员权限
- **路径参数**: `user_id` (UUID)
- **响应**: 204 No Content

#### POST `/users/{user_id}/change-password` - 修改密码
- **描述**: 修改用户密码
- **认证**: 需要访问令牌 + 所有权验证
- **路径参数**: `user_id` (UUID)
- **查询参数**:
  - `old_password`: 当前密码
  - `new_password`: 新密码
- **响应**: 成功消息

#### GET `/users/search` - 搜索用户
- **描述**: 根据关键词搜索用户
- **认证**: 需要访问令牌
- **查询参数**:
  - `q`: 搜索关键词
  - `limit`: 结果数量限制 (默认: 20, 最大: 100)
- **响应**: `List[UserResponse]`

#### GET `/users/statistics` - 用户统计
- **描述**: 获取用户统计信息
- **认证**: 需要管理员权限
- **响应**: 统计数据对象

### 🔧 系统API

#### GET `/api/v1/health` - 健康检查
- **描述**: 检查API服务健康状态
- **响应**: 健康状态信息

#### GET `/api/v1/info` - API信息
- **描述**: 获取API详细信息
- **响应**: API元数据和端点列表

## 🛡️ 安全特性

### 密码安全
- **哈希算法**: bcrypt (12轮)
- **密码强度**: 8维度评分系统
- **密码要求**: 至少8个字符，包含多种字符类型

### JWT安全
- **算法**: HS256
- **JTI**: 防重放攻击
- **过期时间**: 访问令牌30分钟，刷新令牌7天
- **自动刷新**: 支持令牌刷新机制

### 权限控制
- **用户认证**: Bearer Token认证
- **资源所有权**: 用户只能操作自己的资源
- **管理员权限**: 特殊权限用于管理操作

## 📊 错误处理

### 标准HTTP状态码
- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `204 No Content`: 删除成功
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 认证失败
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `409 Conflict`: 数据冲突
- `500 Internal Server Error`: 服务器内部错误

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

## 🧪 测试示例

### Python测试示例
```python
import httpx
import asyncio

async def test_user_workflow():
    async with httpx.AsyncClient() as client:
        # 1. 创建用户
        user_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "confirm_password": "TestPass123!"
        }
        
        response = await client.post(
            "http://localhost:8000/api/v1/users/",
            json=user_data
        )
        assert response.status_code == 201
        user = response.json()
        
        # 2. 用户登录
        login_data = {
            "identifier": "test_user",
            "password": "TestPass123!"
        }
        
        response = await client.post(
            "http://localhost:8000/api/v1/auth/login",
            json=login_data
        )
        assert response.status_code == 200
        tokens = response.json()
        
        # 3. 获取用户信息
        headers = {"Authorization": f"Bearer {tokens['access_token']}"}
        response = await client.get(
            "http://localhost:8000/api/v1/users/me",
            headers=headers
        )
        assert response.status_code == 200
        user_info = response.json()
        
        print(f"用户创建成功: {user_info['username']}")

# 运行测试
asyncio.run(test_user_workflow())
```

### JavaScript测试示例
```javascript
// 使用fetch API的示例
async function testUserAPI() {
    const baseURL = 'http://localhost:8000/api/v1';
    
    // 1. 创建用户
    const createResponse = await fetch(`${baseURL}/users/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: 'js_user',
            email: '<EMAIL>',
            password: 'JSPass123!',
            confirm_password: 'JSPass123!'
        })
    });
    
    const user = await createResponse.json();
    console.log('用户创建成功:', user);
    
    // 2. 用户登录
    const loginResponse = await fetch(`${baseURL}/auth/login`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            identifier: 'js_user',
            password: 'JSPass123!'
        })
    });
    
    const tokens = await loginResponse.json();
    console.log('登录成功:', tokens.user);
    
    // 3. 获取用户信息
    const userResponse = await fetch(`${baseURL}/users/me`, {
        headers: {
            'Authorization': `Bearer ${tokens.access_token}`
        }
    });
    
    const userInfo = await userResponse.json();
    console.log('用户信息:', userInfo);
}

testUserAPI().catch(console.error);
```

## 📖 相关文档

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Pydantic文档](https://docs.pydantic.dev/)
- [SQLAlchemy文档](https://docs.sqlalchemy.org/)
- [JWT规范](https://tools.ietf.org/html/rfc7519)
- [OAuth2规范](https://tools.ietf.org/html/rfc6749)

## 🔗 API文档链接

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json
