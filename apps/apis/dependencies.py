#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 18:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : dependencies.py
# @Update  : 2025/8/7 18:00 API依赖项模块

"""
API依赖项模块

该模块定义了FastAPI应用的所有依赖项，遵循Context7最佳实践：
- 用户认证和授权依赖
- 数据库会话管理
- 请求验证和资源检查
- 分页和查询参数处理

设计原则：
- 单一职责：每个依赖项负责特定的验证或处理逻辑
- 可复用性：依赖项可以在多个端点中重复使用
- 链式依赖：复杂的依赖项可以依赖于更简单的依赖项
- 错误处理：统一的异常处理和错误响应

参考：FastAPI最佳实践和依赖注入模式
"""

from __future__ import annotations

import uuid
from typing import Optional

from fastapi import Depends, HTTPException, Query, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from configs import get_db
from models.users import Users
from schemas.users import UserQuery
from services import auth_service, user_service
from services.base import NotFoundError, ServiceError

# HTTP Bearer认证方案
security = HTTPBearer()


async def get_db_session() -> AsyncSession:
    """
    获取数据库会话依赖项

    Returns:
        AsyncSession: 异步数据库会话

    Example:
        >>> @fast_app.get("/users/")
        >>> async def get_users(session: AsyncSession = Depends(get_db_session)):
        ...     return await user_service.get_multi(session)
    """
    async for session in get_db():
        yield session


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: AsyncSession = Depends(get_db_session)
) -> Users:
    """
    获取当前认证用户依赖项
    
    Args:
        credentials: HTTP Bearer认证凭据
        session: 数据库会话
        
    Returns:
        Users: 当前认证的用户对象
        
    Raises:
        HTTPException: 认证失败时抛出401错误
        
    Example:
        >>> @fast_app.get("/users/me")
        >>> async def get_me(user: Users = Depends(get_current_user)):
        ...     return user
    """
    try:
        # 提取令牌
        token = credentials.credentials
        
        # 验证令牌并获取用户
        user = await auth_service.get_current_user(session, token)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"认证失败: {e.message}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: Users = Depends(get_current_user)
) -> Users:
    """
    获取当前活跃用户依赖项
    
    Args:
        current_user: 当前认证的用户
        
    Returns:
        Users: 当前活跃的用户对象
        
    Raises:
        HTTPException: 用户未激活时抛出403错误
        
    Example:
        >>> @fast_app.get("/protected")
        >>> async def protected_route(user: Users = Depends(get_current_active_user)):
        ...     return {"message": f"Hello {user.username}"}
    """
    # 这里可以添加用户状态检查逻辑
    # 例如检查用户是否被禁用、是否需要验证邮箱等
    
    # 示例：检查用户是否有comment字段表示被禁用
    if current_user.comment and "disabled" in current_user.comment.lower():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户账户已被禁用"
        )
    
    return current_user


async def valid_user_id(
    user_id: uuid.UUID,
    session: AsyncSession = Depends(get_db_session)
) -> Users:
    """
    验证用户ID有效性依赖项
    
    Args:
        user_id: 用户ID
        session: 数据库会话
        
    Returns:
        Users: 验证后的用户对象
        
    Raises:
        HTTPException: 用户不存在时抛出404错误
        
    Example:
        >>> @fast_app.get("/users/{user_id}")
        >>> async def get_user(user: Users = Depends(valid_user_id)):
        ...     return user
    """
    try:
        user = await user_service.get_by_id(session, user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户ID {user_id} 不存在"
            )
        
        return user
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"用户ID {user_id} 不存在"
        )
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户失败: {e.message}"
        )


async def valid_owned_user(
    target_user: Users = Depends(valid_user_id),
    current_user: Users = Depends(get_current_user)
) -> Users:
    """
    验证用户拥有权限依赖项
    
    确保当前用户只能操作自己的资源，或者是管理员。
    
    Args:
        target_user: 目标用户对象
        current_user: 当前认证用户
        
    Returns:
        Users: 验证后的目标用户对象
        
    Raises:
        HTTPException: 权限不足时抛出403错误
        
    Example:
        >>> @fast_app.put("/users/{user_id}")
        >>> async def update_user(
        ...     user_data: UserUpdate,
        ...     user: Users = Depends(valid_owned_user)
        ... ):
        ...     return await user_service.update(session, user.id, user_data)
    """
    # 检查是否是用户本人
    if target_user.id == current_user.id:
        return target_user
    
    # 检查是否是管理员（这里可以根据实际需求调整管理员判断逻辑）
    if current_user.comment and "admin" in current_user.comment.lower():
        return target_user
    
    # 权限不足
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="权限不足：只能操作自己的资源"
    )


def get_user_query_params(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页大小，最大100"),
    username: Optional[str] = Query(None, description="用户名筛选，支持模糊匹配"),
    email: Optional[str] = Query(None, description="邮箱筛选，支持模糊匹配"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向")
) -> UserQuery:
    """
    用户查询参数依赖项
    
    Args:
        page: 页码
        size: 每页大小
        username: 用户名筛选
        email: 邮箱筛选
        sort_by: 排序字段
        sort_order: 排序方向
        
    Returns:
        UserQuery: 用户查询参数对象
        
    Example:
        >>> @fast_app.get("/users/")
        >>> async def list_users(
        ...     query: UserQuery = Depends(get_user_query_params),
        ...     session: AsyncSession = Depends(get_db_session)
        ... ):
        ...     return await user_service.get_users(session, query)
    """
    return UserQuery(
        page=page,
        size=size,
        username=username,
        email=email,
        sort_by=sort_by,
        sort_order=sort_order
    )


async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    session: AsyncSession = Depends(get_db_session)
) -> Optional[Users]:
    """
    获取可选的当前用户依赖项
    
    用于那些可以匿名访问，但如果提供了认证信息则返回用户的端点。
    
    Args:
        credentials: 可选的HTTP Bearer认证凭据
        session: 数据库会话
        
    Returns:
        Optional[Users]: 当前用户对象，如果未认证则返回None
        
    Example:
        >>> @fast_app.get("/public-endpoint")
        >>> async def public_endpoint(
        ...     user: Optional[Users] = Depends(get_optional_current_user)
        ... ):
        ...     if user:
        ...         return {"message": f"Hello {user.username}"}
        ...     return {"message": "Hello anonymous user"}
    """
    if not credentials:
        return None
    
    try:
        token = credentials.credentials
        user = await auth_service.get_current_user(session, token)
        return user
    except Exception:
        # 认证失败时返回None而不是抛出异常
        return None


# 管理员权限依赖项
async def require_admin_user(
    current_user: Users = Depends(get_current_active_user)
) -> Users:
    """
    要求管理员权限依赖项
    
    Args:
        current_user: 当前活跃用户
        
    Returns:
        Users: 管理员用户对象
        
    Raises:
        HTTPException: 非管理员用户时抛出403错误
        
    Example:
        >>> @fast_app.delete("/users/{user_id}")
        >>> async def delete_user(
        ...     user_id: uuid.UUID,
        ...     admin: Users = Depends(require_admin_user),
        ...     session: AsyncSession = Depends(get_db_session)
        ... ):
        ...     return await user_service.delete(session, user_id)
    """
    # 检查管理员权限（这里可以根据实际需求调整判断逻辑）
    if not current_user.comment or "admin" not in current_user.comment.lower():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    return current_user
