-- EchoNote 数据库初始化脚本
-- 创建测试数据库
-- CREATE DATABASE echo_note;

-- 为数据库启用扩展
\c echo_note;
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS age;

-- 加载AGE扩展
LOAD 'age';

-- 设置 schema 路径（必须）
SET search_path = ag_catalog, "$user", public;

-- 创建图数据库（如果不存在）
DO $$
BEGIN
    PERFORM ag_catalog.create_graph('echo_note_graph');
    RAISE NOTICE 'Graph echo_note_graph created successfully';
EXCEPTION 
    WHEN duplicate_object THEN
        RAISE NOTICE 'Graph echo_note_graph already exists';
    WHEN OTHERS THEN
        RAISE NOTICE 'Failed to create graph: %', SQLERRM;
END $$;

-- 显示已安装的扩展
SELECT extname, extversion FROM pg_extension WHERE extname IN ('vector', 'age');

-- 验证AGE功能
SELECT name FROM ag_catalog.ag_graph WHERE name = 'echo_note_graph';