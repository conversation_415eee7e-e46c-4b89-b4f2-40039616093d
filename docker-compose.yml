# EchoNote 开发环境 Docker Compose 配置文件
# 基于 echo_note_docks 项目配置，支持 pgvector 和 Apache AGE 扩展

version: '3.8'

services:
  # PostgreSQL 数据库服务（支持 pgvector 和 Apache AGE）
  postgres:
    build:
      context: ./docker
      dockerfile: Dockerfile
    container_name: echo_note-postgres-dev
    restart: unless-stopped
    environment:
      - POSTGRES_DB=echo_note
      - POSTGRES_USER=echo_note
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db.sql:/docker-entrypoint-initdb.d/01-init-db.sql
    ports:
      - "5432:5432"
    networks:
      - echo_note-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U echo_note -d echo_note"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis 缓存服务（带密码认证）
  redis:
    image: redis:7-alpine
    container_name: echo_note-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass password
    volumes:
      - redis_data:/data
    ports:
      - "6380:6379"
    networks:
      - echo_note-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "password", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # PostgreSQL管理工具 (可选)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: echo_note-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - echo_note-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  echo_note-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
